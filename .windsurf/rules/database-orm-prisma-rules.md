---
description: 数据库操作与ORM框架开发使用规范
globs:
alwaysApply: false
---

- 涉及需要实体类开发时，优先查阅 `packages/db/prisma/models目录下的所有实体定义`，明确数据结构后再开发。
- 实体类型优先导入 ORM 框架自动生成的类型。
- 数据库单表或多表复合操作必须确保事务一致性。
- 生成表结构时，不要使用数据库支持枚举，使用常量枚举代替。
- 生成表结构时，字段必须增加 `@map('underline_case')` 定义数据库名称，表名必须以`s_`(必需表)、`t_`(普通表)前缀命名别使用 `@@map('t_underline_case')` 定义


---
description: 聚焦Prisma与Postgresql集成的数据库最佳实践
globs: prisma/​**​/*, src/db/​**​/*, ​**​/*.prisma,
---

# 数据库最佳实践

## Prisma配置
- 采用合理的模式设计
- 实施规范的迁移流程
- 使用正确的关系定义
- 配置适当的连接方式
- 实现标准化的数据填充
- 采用规范的客户端设置

## Prisma模型
- 使用规范的模型命名
- 实现正确的关系关联
- 采用适当的字段类型
- 定义合理的索引
- 实施规范的约束条件
- 使用标准化的枚举类型

## Prisma查询
- 进行查询优化
- 实现正确的过滤机制
- 采用合理的关系加载
- 规范处理事务
- 实现标准分页
- 使用聚合函数

## 数据库设计
- 采用合理的规范化设计
- 实现规范的索引策略
- 使用适当的约束条件
- 定义正确的关系模型
- 实施级联操作
- 采用合适的数据类型

## 性能优化
- 使用连接池技术
- 实现缓存机制
- 进行查询优化
- 处理N+1查询问题
- 实施批量操作
- 监控性能指标

## 安全防护
- 采用规范的认证方式
- 实现适当的授权机制
- 正确处理敏感数据
- 使用标准加密方案
- 实施定期备份
- 监控安全隐患

## 最佳实践
- 遵循数据库规范
- 使用标准迁移流程
- 实现版本控制
- 规范错误处理
- 完善模式文档
- 监控数据库健康状态
