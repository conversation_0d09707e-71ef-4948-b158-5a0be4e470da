---
trigger: model_decision
description: 强制项目目录采用一致的命名规范。
globs: **/*
---
- 组件目录优先使用 PascalCase 命名法，组件采用具名导出，示例：(components/Wizard.tsx)，整体模块通过 index.ts 统一导出。
- 非组件代码文件使用 kebab-case 命名法，示例：`consts/users.ts`。
- 常量采用 SCREAMING_SNAKE_CASE 命名法，示例：`const AUTH_USER_KEY = 'USER_AUTH'`。
- 枚举类定义时，枚举键使用 PascalCase 命名法，枚举值使用 snake_case 命名法，示例：`enum UserRole { AdminRole = 'admin_role' }`。

示例：

```
// (components)
- Wizard.tsx