---
trigger: model_decision
description: 国际化使用规范
globs: app/**.ts
---

- 使用 `next-intl` 框架对项目进行国际化支持，参考 [next-intl文档](mdc:qizhilu-platform/https:/next-intl.dev/docs/getting-started) 了解API使用
- 集成国际化后，路由 API 需要使用 `@repo/i18n` 内导出的 `useRouter` `Link`  `useLocale` 等相关函数和组件，确保路由能正确处理国际化请求。
- 需要国际化支持的页面必须在 `app/[locale]` 目录下
- 国际化文本存放在 `messages` 目录下的语言编码（如 `en`、`zh-HK`、`zh-CN`）目录下，
- 国际化文本按照页面、组件、业务功能、系统功能等关注点做分离，分离的文本使用不同的`json`文件以及独立的命名空间存放，并在 `index.ts`处结构导出应用。
- 所有文本必须在文件同名的命名空间下，文本文件名与命名空间使用 PascalCase 命名。
- 语言默认支持 `zh-CN`, 其他语言支持关注 `README.md` 中说明（非必要不支持）
示例:
```ts
// 目录
- messages
    - en
        - Common.json
        - index.ts
    - zh-HK
        - Common.json
        - LoginForm.json
        - index.ts
    - zh-CN
        - Common.json
        - index.ts

// Common.json
{
    // 所有文本必须在文件同名的命名空间下
    "Common": {
        "title": "Common",
        "description": "Common"
    }
}

// index.ts
import Common from "./Common.json"

// 结构导出
export default {
    ...Common
}

```
