---
trigger: glob
description:
globs: **/app/api/**/*.ts
---
# API端点路由规范

## 文件路径与命名

1. **标准路径模板**: 所有API端点必须遵循 `app/api/[资源]/[[...slug]]/route.ts` 的路径模板
2. **资源命名**: 使用kebab-case（短横线）命名资源，例如 `new-game`、`user-profile`
3. **路由文件**: 所有API端点必须在 `route.ts` 文件中定义

## 代码结构规范

1. **路由初始化**:
   - 使用 `AutoRouter` 初始化路由，并指定正确的基础路径
   - 基础路径必须与文件路径保持一致

2. **数据验证**:
   - 使用 `zod` 库进行请求数据验证
   - 为每个请求类型定义明确的验证schema
   - 对部分更新操作使用 `.partial()` 方法

3. **身份验证**:
   - 每个需要权限的端点必须在处理请求前执行身份验证
   - 使用 `authUser()` 函数进行用户身份验证
   - 未认证请求立即返回 401 状态码

4. **标准CRUD操作**:
   - 创建记录: POST 请求到资源根路径 `'/'`
   - 获取单条记录: GET 请求到 `'/:id'`
   - 更新记录: PUT 请求到 `'/:id'`
   - 删除记录: DELETE 请求到 `'/:id'`
   - 列表查询: GET 请求到资源根路径 `'/'`

5. **分页与查询**:
   - 列表查询必须支持分页功能
   - 默认每页10条记录，最大不超过20条
   - 支持通过查询参数筛选结果
   - 返回分页元数据（总数、当前页、每页条数）

6. **响应格式**:
   - 使用 `R.ok()` 返回成功响应
   - 使用 `R.notFound()` 返回404错误
   - 使用 `R.raw()` 返回自定义状态码和消息

7. **导出处理函数**:
   - 必须导出与支持的HTTP方法对应的处理函数
   - 所有处理函数必须调用 `router.fetch(request)`

## 安全与性能

1. **数据过滤**:
   - 查询必须限制在用户可访问的记录范围内
   - 使用用户ID等字段进行数据隔离

2. **错误处理**:
   - 所有数据库操作必须包含适当的错误处理
   - 永远不要将内部错误详情暴露给客户端

3. **请求限制**:
   - 分页查询必须限制最大记录数
   - 对请求体大小进行合理限制

## 示例模板

```javascript
import { AutoRouter } from 'itty-router'
import { prisma } from '@repo/db'
import { z } from 'zod'
import { R } from '@repo/utils/server'
import { authUser } from '@repo/auth/server'
import { getUserCookies } from '@repo/auth/server'

const router = AutoRouter({ base: '/api/resource-name' })

// 验证schema
const createSchema = z.object({
  // 定义字段验证规则
})

// CRUD操作实现
// ...

export const GET = (request: Request) => router.fetch(request)
export const POST = (request: Request) => router.fetch(request)
export const PUT = (request: Request) => router.fetch(request)
export const DELETE = (request: Request) => router.fetch(request)
```
