---
trigger: model_decision
description: 客户端组件中的数据获取规范。
globs: **/app/**/*.tsx
---
# NextJS 客户端组件数据获取规范

## API 集成
- 站点内所有 fetch 请求必须统一使用 `@repo/utils/react` 提供的工具方法，以实现统一的错误处理
- 可用的请求方法包括：`fetchGet`、`fetchPost`、`fetchPut`、`fetchDel`

## 查询模式
1. **常规查询**
   - 查询场景统一使用 `swr` 框架
   - 合理配置 revalidation（重新验证）参数
   - 示例：
   ```ts
   const { data, error, isLoading, mutate } = useSWR<ResponseType>('/api/endpoint', fetchGet, {
     revalidateOnFocus: false,
     revalidateOnReconnect: false,
     revalidateOnMount: false,
   });

2. **分页查询**
   - 使用 @lib/types 中的 Pageable 模型
   - 服务端实现需使用 offsetPage 和 stripPage 工具方法
   - 客户端需维护分页状态，并将其包含在请求参数中

3. **数据更新**
   - 根据操作类型选择合适的 fetch 方法（POST、PUT、DELETE）
   - 错误处理需结合 toast 消息提示
   - 适当场景下通过 SWR 的 mutate 方法刷新数据
