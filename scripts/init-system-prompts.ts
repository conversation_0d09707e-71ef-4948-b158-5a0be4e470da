#!/usr/bin/env tsx

/**
 * 初始化系统默认提示词脚本
 * 运行方式：npx tsx scripts/init-system-prompts.ts
 */

import { prisma } from "@repo/db"
import { PromptService } from "@repo/utils/server"
import { getLogger } from "@repo/logger"

const logger = getLogger("InitSystemPrompts")

async function main() {
  try {
    logger.info("开始初始化系统默认提示词...")
    
    // 初始化系统默认提示词
    await PromptService.initializeSystemPrompts()
    
    logger.info("系统默认提示词初始化完成")
    
    // 查询并显示已创建的提示词
    const systemPrompts = await prisma.promptTemplate.findMany({
      where: {
        projectId: null,
        isDefault: true
      },
      select: {
        id: true,
        type: true,
        name: true,
        isEnabled: true
      }
    })
    
    logger.info(`已创建 ${systemPrompts.length} 个系统默认提示词:`)
    systemPrompts.forEach(prompt => {
      logger.info(`- ${prompt.name} (${prompt.type}) - ${prompt.isEnabled ? '启用' : '禁用'}`)
    })
    
  } catch (error) {
    logger.error("初始化系统默认提示词失败:", error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
