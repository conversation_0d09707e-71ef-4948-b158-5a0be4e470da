#!/usr/bin/env tsx

/**
 * 测试提示词服务脚本
 * 运行方式：npx tsx scripts/test-prompt-service.ts
 */

import { PromptService } from "@repo/utils/server"
import { PROMPT_TEMPLATE_TYPE } from "@repo/shared-types"
import { getLogger } from "@repo/logger"

const logger = getLogger("TestPromptService")

async function main() {
  try {
    logger.info("开始测试提示词服务...")
    
    // 测试获取翻译提示词
    logger.info("测试获取翻译提示词...")
    const translationPrompt = await PromptService.getTranslationPrompt("ProjectGameLocale_metadata")
    logger.info("翻译提示词获取成功，长度:", translationPrompt.length)
    
    // 测试构建翻译提示词
    logger.info("测试构建翻译提示词...")
    const builtPrompt = await PromptService.buildTranslationPrompt(
      "ProjectGameLocale_metadata",
      "zh-CN",
      "en-US",
      "这是一个测试游戏"
    )
    logger.info("构建的提示词:")
    console.log(builtPrompt)
    
    // 测试变量替换
    logger.info("测试变量替换...")
    const template = "将 {content} 从 {sourceLanguage} 翻译为 {targetLanguage}"
    const replaced = PromptService.replaceVariables(template, {
      content: "Hello World",
      sourceLanguage: "英语",
      targetLanguage: "中文"
    })
    logger.info("替换结果:", replaced)
    
    // 测试获取生成提示词
    logger.info("测试获取生成提示词...")
    const generationPrompt = await PromptService.buildGenerationPrompt(
      "article",
      {
        gameInfo: "一个有趣的益智游戏",
        wordCount: 500,
        language: "zh-CN"
      }
    )
    logger.info("生成提示词获取成功，长度:", generationPrompt.length)
    
    logger.info("提示词服务测试完成")
    
  } catch (error) {
    logger.error("测试提示词服务失败:", error)
    process.exit(1)
  }
}

main()
