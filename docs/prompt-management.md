# 提示词管理系统

本文档介绍新增的提示词管理功能，包括项目级和系统级提示词管理。

## 功能概述

### 1. 提示词管理功能
- **项目级提示词管理**：每个项目可以自定义自己的提示词模板
- **系统级提示词管理**：管理员可以设置系统默认提示词
- **提示词类型**：支持多种预定义的提示词类型
- **CRUD 操作**：完整的创建、读取、更新、删除功能

### 2. 提示词导入功能
- 支持从其他项目导入提示词
- 批量导入，自动覆盖同类型提示词
- 导入结果详细反馈

### 3. 提示词测试功能
- 在线测试提示词效果
- 支持自定义测试参数
- 每日测试次数限制（默认20次）
- 测试结果和性能统计

### 4. 动态提示词集成
- Scheduler 模块自动使用动态提示词
- 优先级：项目级 > 系统级 > 默认提示词
- 向后兼容，确保现有功能正常运行

## 提示词类型

系统支持以下提示词类型：

### 翻译类提示词
- `TRANSLATION_GAME_CONTENT`：游戏内容翻译
- `TRANSLATION_ARTICLE`：文章翻译
- `TRANSLATION_METADATA`：元数据翻译
- `TRANSLATION_SITE_SETTING`：站点设置翻译

### 生成类提示词
- `GAME_ARTICLE_GENERATION`：游戏文章生成
- `GAME_COPY_GENERATION`：游戏文案生成
- `GAME_FAQ_GENERATION`：游戏FAQ生成
- `GAME_COMMENT_GENERATION`：游戏评论生成

## 使用指南

### 项目级提示词管理

1. **访问提示词管理页面**
   ```
   /project/{project_id}/prompts
   ```

2. **创建新提示词**
   - 点击"新建提示词"按钮
   - 选择提示词类型
   - 填写名称、描述和内容
   - 保存提示词

3. **编辑提示词**
   - 在列表中点击编辑按钮
   - 修改提示词内容
   - 保存更改

4. **导入提示词**
   - 点击"导入提示词"按钮
   - 输入源项目ID
   - 确认导入

5. **查看系统默认提示词**
   - 项目中不存在的提示词类型会显示系统默认提示词
   - 点击查看按钮可以查看详细内容
   - 系统默认提示词标有"系统默认"标签

6. **基于系统提示词创建项目提示词**
   - 点击系统默认提示词的编辑按钮
   - 系统会自动基于系统提示词创建项目级提示词
   - 创建后可以进一步编辑和自定义

7. **测试提示词**
   - 点击测试按钮（仅项目级提示词可测试）
   - 输入测试参数（JSON格式）
   - 查看测试结果

### 系统级提示词管理

1. **访问系统管理后台**
   ```
   http://localhost:3001/prompts
   ```

2. **管理系统默认提示词**
   - 查看所有系统级提示词
   - 编辑默认提示词内容
   - 启用/禁用提示词

### API 使用

#### 项目级提示词API

**获取提示词列表**
```typescript
GET /api/prompts?projectId={projectId}&type={type}&page={page}&limit={limit}&includeSystemDefaults={true}
```
- `includeSystemDefaults=true`: 包含系统默认提示词（项目中不存在的类型会显示系统默认提示词）

**获取提示词详情**
```typescript
GET /api/prompts/detail?id={promptId}
```

**创建提示词**
```typescript
POST /api/prompts/create
{
  "projectId": "project_id",
  "type": "TRANSLATION_GAME_CONTENT",
  "name": "游戏内容翻译",
  "description": "用于翻译游戏内容的提示词",
  "content": "提示词内容...",
  "isEnabled": true
}
```

**更新提示词**
```typescript
POST /api/prompts/update
{
  "id": "prompt_id",
  "projectId": "project_id",
  "name": "游戏内容翻译",
  "description": "用于翻译游戏内容的提示词",
  "content": "提示词内容...",
  "isEnabled": true
}
```

**删除提示词**
```typescript
POST /api/prompts/delete
{
  "id": "prompt_id"
}
```

**导入提示词**
```typescript
POST /api/prompts/import
{
  "sourceProjectId": "source_project_id",
  "targetProjectId": "target_project_id"
}
```

**测试提示词**
```typescript
POST /api/prompts/test
{
  "promptTemplateId": "prompt_id",
  "projectId": "project_id",
  "inputParams": {
    "sourceLanguage": "zh-CN",
    "targetLanguage": "en-US",
    "content": "测试内容"
  }
}
```

**基于系统提示词创建项目提示词**
```typescript
POST /api/prompts/create-from-system
{
  "systemPromptId": "system_prompt_id",
  "projectId": "project_id"
}
```

#### 系统级提示词API

**获取系统提示词列表**
```typescript
GET /api/system-prompts?type={type}&page={page}&limit={limit}
```

**获取系统提示词详情**
```typescript
GET /api/system-prompts/detail?id={promptId}
```

**创建系统提示词**
```typescript
POST /api/system-prompts/create
{
  "type": "TRANSLATION_GAME_CONTENT",
  "name": "系统默认游戏内容翻译",
  "description": "系统默认的游戏内容翻译提示词",
  "content": "提示词内容...",
  "isEnabled": true
}
```

**更新系统提示词**
```typescript
POST /api/system-prompts/update
{
  "id": "prompt_id",
  "name": "系统默认游戏内容翻译",
  "description": "系统默认的游戏内容翻译提示词",
  "content": "提示词内容...",
  "isEnabled": true
}
```

**删除系统提示词**
```typescript
POST /api/system-prompts/delete
{
  "id": "prompt_id"
}
```

**初始化系统默认提示词**
```typescript
POST /api/system-prompts/initialize
{}
```

### 程序化使用

#### 在代码中使用提示词服务

```typescript
import { PromptService } from "@repo/utils/server"

// 获取翻译提示词
const prompt = await PromptService.getTranslationPrompt("ProjectGameLocale_metadata", projectId)

// 构建完整的翻译提示词
const fullPrompt = await PromptService.buildTranslationPrompt(
  "ProjectGameLocale_metadata",
  "zh-CN",
  "en-US",
  "要翻译的内容",
  projectId
)

// 获取生成类提示词
const generationPrompt = await PromptService.buildGenerationPrompt(
  "article",
  {
    gameInfo: "游戏信息",
    wordCount: 500,
    language: "zh-CN"
  },
  projectId
)
```

## 数据库模型

### PromptTemplate（提示词模板）
- `id`：提示词ID
- `projectId`：项目ID（null表示系统级）
- `type`：提示词类型
- `name`：提示词名称
- `description`：描述
- `content`：提示词内容
- `isEnabled`：是否启用
- `isDefault`：是否为系统默认
- `createdBy`：创建者ID

### PromptTestRecord（提示词测试记录）
- `id`：测试记录ID
- `promptTemplateId`：提示词模板ID
- `projectId`：项目ID
- `userId`：用户ID
- `inputParams`：测试输入参数
- `outputResult`：测试输出结果
- `status`：测试状态
- `duration`：耗时

### ProjectPromptUsageLimit（项目提示词使用限制）
- `id`：记录ID
- `projectId`：项目ID
- `date`：日期
- `usedTestCount`：已使用测试次数
- `maxTestCount`：最大测试次数限制

## 初始化和部署

### 1. 初始化系统默认提示词
```bash
npx tsx scripts/init-system-prompts.ts
```

### 2. 测试提示词服务
```bash
npx tsx scripts/test-prompt-service.ts
```

### 3. 启动 Dashboard 应用
```bash
cd apps/dashboard
npm run dev
```

### 4. 数据库迁移
确保运行 Prisma 迁移以创建新的数据表：
```bash
npx prisma migrate dev
```

## 注意事项

1. **权限控制**：系统级提示词管理需要管理员权限
2. **测试限制**：每个项目每天最多测试20次
3. **向后兼容**：现有翻译功能会自动使用新的提示词系统
4. **降级机制**：如果动态提示词获取失败，会自动降级到默认提示词
5. **性能考虑**：提示词内容会被缓存，避免频繁数据库查询

## 故障排除

### 常见问题

1. **提示词测试失败**
   - 检查测试参数格式是否正确
   - 确认是否超过每日测试限制
   - 查看错误日志获取详细信息

2. **翻译任务使用旧提示词**
   - 确认项目级提示词是否启用
   - 检查提示词类型映射是否正确
   - 重启 Scheduler 服务

3. **系统提示词无法访问**
   - 确认用户是否有管理员权限
   - 检查 Dashboard 应用是否正常运行
   - 验证数据库连接

### 日志查看

相关日志标签：
- `PromptService`：提示词服务日志
- `TranslationTaskListener`：翻译任务日志
- `PromptAPI`：提示词API日志
