# 后台任务系统

## 概述

后台任务系统是一个异步任务处理框架，主要用于处理翻译任务等耗时操作。系统采用消息队列模式，确保任务的可靠执行和状态跟踪。

## 系统架构

```
用户操作 → 判断是否需要翻译 → 创建后台任务 → 发送MQ消息
                                              ↓
Scheduler消费消息 → 执行翻译 → 保存结果 → 更新任务状态
                                              ↓
用户在任务管理页面查看进度、重试失败任务
```

## 核心组件

### 1. 数据库模型
- **BackgroundTask**: 后台任务表，存储任务信息、状态、参数等
- 位置: `packages/db/prisma/models/task.prisma`

### 2. 任务状态常量
- **BACKGROUND_TASK_STATUS**: 任务状态常量
- **BACKGROUND_TASK_TYPE**: 任务类型常量
- 位置: `packages/shared-types/src/consts/tasks.ts`

### 3. 服务层
- **BackgroundTaskService.server.ts**: 服务端任务管理服务
- 功能: 创建任务、更新状态、重试机制等

### 4. 消费者
- **TranslationTaskListener.ts**: 翻译任务消费者
- 功能: 消费MQ消息，执行翻译，更新状态

### 5. 管理界面
- **background-tasks/page.tsx**: 任务管理页面
- 功能: 查看任务列表、重试失败任务、删除任务

## 使用方式

### 1. 自动创建翻译任务

#### 项目设置翻译

在保存项目设置时，系统会自动判断是否需要创建翻译任务：

```typescript
// 在API路由中
const response = await fetchPost(`/api/project-locale-site-settings`, {
  projectId,
  locale: currentLanguage,
  type: ProjectLocaleSiteSettingType.Metadata,
  content: dataToSave,
  createTranslationTask: true, // 标记需要创建翻译任务
})
```

#### 游戏内容翻译

在保存游戏内容时，系统也支持自动创建翻译任务：

```typescript
// 单个游戏内容保存
const response = await fetchPost(`/api/project-games/content`, {
  projectId,
  gameId,
  locale: currentLanguage,
  type: 'metadata', // metadata, content, basicInfo
  content: gameContentData,
  createTranslationTask: true, // 标记需要创建翻译任务
})

// 批量游戏内容保存
const response = await fetchPost(`/api/project-games/content/list`, {
  projectId,
  gameId,
  locale: currentLanguage,
  type: 'content',
  contents: gameContentsArray,
  createTranslationTask: true, // 标记需要创建翻译任务
})
```

### 2. 手动创建翻译任务

```typescript
import { createTranslationTask } from '@/lib/services/BackgroundTaskService.server'

// 项目设置翻译任务
const taskId = await createTranslationTask(projectId, userId, {
  contentType: 'Metadata',
  contentId: 'content-id',
  sourceLocale: 'zh',
  targetLocales: ['en', 'ja'],
  fieldsToTranslate: ['title', 'description']
})

// 游戏内容翻译任务
const gameTaskId = await createTranslationTask(projectId, userId, {
  contentType: 'ProjectGameLocale_metadata',
  contentId: 'game-content-id',
  sourceLocale: 'zh',
  targetLocales: ['en', 'ja'],
  fieldsToTranslate: ['title', 'description', 'ogTitle', 'ogDescription'],
  gameId: 'game-id',
  gameName: '游戏名称'
})
```

### 3. 查看任务状态

用户可以在项目管理页面的"后台任务"菜单中查看所有任务的状态和进度。

## 翻译判断逻辑

### 创建翻译任务的条件

系统会在以下情况下创建翻译任务：

1. **默认语言检查**: 只有当保存的内容是项目默认语言时才考虑创建翻译任务
2. **内容变化检测**:
   - 新创建内容：直接创建翻译任务
   - 更新内容：对比新旧内容的翻译字段，只有发生变化时才创建任务
3. **目标语言筛选**: 只为以下语言创建翻译任务：
   - 该语言的内容不存在
   - 该语言的内容存在但翻译字段为空或空字符串

### 具体实现

每个API都有自己的判断逻辑实现：

#### 项目设置API
- 函数: `shouldCreateProjectSettingTranslationTask()`
- 检查: ProjectLocaleSiteSetting表中的内容变化
- 字段映射: 根据设置类型确定需要翻译的字段

#### 游戏内容API
- 函数: `shouldCreateGameTranslationTask()`
- 检查: ProjectGameLocale表中的内容变化
- 字段映射: 根据游戏内容类型确定需要翻译的字段
- 关联检查: 通过contentId关联同一游戏内容的不同语言版本

## 任务状态

- **PENDING**: 待处理
- **PROCESSING**: 处理中
- **SUCCESS**: 处理成功
- **FAILED_QUEUED**: 失败进入队列（重试中）
- **FAILED**: 处理失败

## 重试机制

- 网络等可重试错误：自动重试3次，每次间隔5秒
- 超过重试次数：重新加入MQ队列排队
- 整体重试次数：不超过3次
- 超过整体重试次数：标记为失败

## 配置

### 环境变量

```env
PROJECT_QUEUE_NAME=your_project_queue_name
TASK_QUEUE_NAME=your_task_queue_name
```

### 队列配置

- 翻译任务队列: `${PROJECT_QUEUE_NAME}_translation_tasks`
- 并发消费数量: 5个任务同时处理

## 支持的内容类型

### 项目设置类型
- **Metadata**: 项目元数据（title, description等）
- **Nav**: 导航菜单
- **GameCategories**: 游戏分类
- **ArticleCategories**: 文章分类

### 游戏内容类型
- **ProjectGameLocale_metadata**: 游戏元数据（SEO信息）
  - 翻译字段: title, name, description, ogTitle, ogDescription, twitterTitle, twitterDescription
- **ProjectGameLocale_content**: 游戏详情内容
  - 翻译字段: title, description, content, text
- **ProjectGameLocale_basicInfo**: 游戏基本信息
  - 翻译字段: gameName, gameDescription, name, description

## 扩展

### 添加新的任务类型

1. 在 `BACKGROUND_TASK_TYPE` 中添加新类型
2. 在 `BackgroundTaskService.server.ts` 中添加创建逻辑
3. 创建对应的消费者类
4. 在scheduler中注册消费者

### 添加新的内容类型翻译

1. 在相应的API路由中添加 `createTranslationTask` 参数支持
2. 在 `getFieldsToTranslate` 函数中添加新类型的字段映射
3. 在 `TranslationTaskListener.ts` 中的 `saveTranslationResults` 方法中添加处理逻辑

### 自定义翻译服务

修改 `TranslationTaskListener.ts` 中的 `simpleTranslate` 方法，集成真实的翻译API（如Google Translate、百度翻译等）。

## 监控和日志

- 所有任务操作都有详细的日志记录
- 可通过任务管理页面查看任务执行结果和错误信息
- 支持任务统计和状态分析

## 注意事项

1. 确保MQ服务正常运行
2. Scheduler模块需要正常启动才能处理任务
3. 翻译任务需要有效的项目语言配置
4. 删除任务前确保任务已完成或失败
