import { getLogger } from "@repo/logger"
import { TaskQueueListener } from "@repo/utils/amqp"
import { prisma } from "@repo/db"
import { RpaMessage, BACKGROUND_TASK_STATUS, BACKGROUND_TASK_TYPE } from "@repo/shared-types"
import { AMQPClient } from "@repo/utils/amqp"
import { buildTranslationPrompt } from "@repo/utils/server"
import { translateText } from "@repo/utils/server"

const logger = getLogger("TranslationTaskListener")

interface TranslationTaskMessage {
  taskId: string
  type: string
  projectId: string
  userId: string
  parameters: {
    contentType: string
    contentId: string
    sourceLocale: string
    targetLocales: string[]
    fieldsToTranslate?: string[]
    extraParams?: Record<string, any>
  }
}

export class TranslationTaskListener extends TaskQueueListener {
  constructor(mqClient: AMQPClient) {
    const queueName = `${process.env.TASK_QUEUE_NAME}_translation_tasks`
    super(mqClient, queueName, "")
  }

  async execute(message: RpaMessage): Promise<any> {
    const taskData = message.data as unknown as TranslationTaskMessage
    const { taskId, parameters } = taskData
    const { contentType, contentId, sourceLocale, targetLocales, fieldsToTranslate } = parameters

    logger.info(`[TranslationTaskListener] 开始处理翻译任务`, { taskId, contentType, contentId })

    try {
      // 更新任务状态为处理中
      await this.updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.PROCESSING)

      // 获取源内容
      const sourceContent = await this.getSourceContent(contentType, contentId, sourceLocale)
      if (!sourceContent) {
        throw new Error(`未找到源内容: ${contentType}/${contentId}/${sourceLocale}`)
      }

      // 执行翻译
      const translationResults = await this.translateContent(
        sourceContent,
        sourceLocale,
        targetLocales,
        fieldsToTranslate,
        contentType,
        taskData.projectId
      )

      // 保存翻译结果
      await this.saveTranslationResults(
        contentType,
        contentId,
        sourceLocale,
        translationResults
      )

      // 更新任务状态为成功
      await this.updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.SUCCESS, {
        translatedLocales: Object.keys(translationResults),
        translationCount: Object.keys(translationResults).length
      })

      logger.info(`[TranslationTaskListener] 翻译任务完成`, { taskId, translatedLocales: Object.keys(translationResults) })

    } catch (error) {
      logger.error(`[TranslationTaskListener] 翻译任务失败`, { taskId, error: error.message })

      // 获取当前任务信息
      const task = await prisma.backgroundTask.findUnique({
        where: { id: taskId }
      })

      if (!task) {
        logger.error(`[TranslationTaskListener] 任务不存在: ${taskId}`)
        return
      }

      // 增加重试次数
      const newRetryCount = task.retryCount + 1

      if (newRetryCount < task.maxRetries) {
        // 还可以重试，更新重试次数并重新入队
        await prisma.backgroundTask.update({
          where: { id: taskId },
          data: {
            retryCount: newRetryCount,
            errorMessage: error.message,
            updatedAt: new Date()
          }
        })

        // 延迟后重新发送到队列
        setTimeout(async () => {
          try {
            await this.requeueTask(taskData)
            logger.info(`[TranslationTaskListener] 任务重新入队`, { taskId, retryCount: newRetryCount })
          } catch (requeueError) {
            logger.error(`[TranslationTaskListener] 重新入队失败`, { taskId, error: requeueError.message })
          }
        }, 5000) // 5秒后重试

      } else {
        // 超过最大重试次数，标记为失败
        await this.updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.FAILED, undefined, error.message)
      }
    }
  }

  private async getSourceContent(contentType: string, contentId: string, sourceLocale: string): Promise<any> {
    switch (contentType) {
      case "Metadata":
      case "Nav":
      case "GameCategories":
      case "ArticleCategories":
        const setting = await prisma.projectLocaleSiteSetting.findUnique({
          where: { id: contentId }
        })
        return setting?.content

      default:
        throw new Error(`不支持的内容类型: ${contentType}`)
    }
  }

  private async translateContent(
    sourceContent: any,
    sourceLocale: string,
    targetLocales: string[],
    fieldsToTranslate?: string[],
    contentType?: string,
    projectId?: string
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {}

    for (const targetLocale of targetLocales) {
      try {
        // 使用动态提示词进行翻译
        const translatedContent = await this.translateWithPrompt(
          sourceContent,
          sourceLocale,
          targetLocale,
          fieldsToTranslate,
          contentType,
          projectId
        )

        results[targetLocale] = translatedContent
      } catch (error) {
        logger.error(`[TranslationTaskListener] 翻译到 ${targetLocale} 失败`, { error: error.message })
        // 继续处理其他语言
      }
    }

    return results
  }

  private async translateWithPrompt(
    sourceContent: any,
    sourceLocale: string,
    targetLocale: string,
    fieldsToTranslate?: string[],
    contentType?: string,
    projectId?: string
  ): Promise<any> {
    try {
      // 如果指定了要翻译的字段，只翻译这些字段
      if (fieldsToTranslate && fieldsToTranslate.length > 0) {
        const translated = JSON.parse(JSON.stringify(sourceContent))

        for (const field of fieldsToTranslate) {
          if (this.hasNestedProperty(translated, field)) {
            const value = this.getNestedProperty(translated, field)
            if (typeof value === 'string' && value.trim()) {
              // 使用动态提示词进行翻译
              const prompt = await buildTranslationPrompt(
                contentType || 'default',
                sourceLocale,
                targetLocale,
                value,
                projectId
              )

              const translationResult = await translateText(value, {
                sourceLanguage: sourceLocale,
                targetLanguage: targetLocale,
                customPrompt: prompt
              })

              this.setNestedProperty(translated, field, translationResult.translatedText)
            }
          }
        }

        return translated
      } else {
        // 翻译整个内容
        const contentStr = typeof sourceContent === 'string'
          ? sourceContent
          : JSON.stringify(sourceContent, null, 2)

        const prompt = await buildTranslationPrompt(
          contentType || 'default',
          sourceLocale,
          targetLocale,
          contentStr,
          projectId
        )

        const translationResult = await translateText(contentStr, {
          sourceLanguage: sourceLocale,
          targetLanguage: targetLocale,
          customPrompt: prompt
        })

        // 尝试解析为JSON，如果失败则返回字符串
        try {
          return JSON.parse(translationResult.translatedText)
        } catch {
          return translationResult.translatedText
        }
      }
    } catch (error) {
      logger.error(`[TranslationTaskListener] 使用提示词翻译失败，降级到简单翻译`, { error: error.message })
      // 降级到简单翻译
      return await this.simpleTranslate(sourceContent, sourceLocale, targetLocale, fieldsToTranslate)
    }
  }

  private async simpleTranslate(
    sourceContent: any,
    sourceLocale: string,
    targetLocale: string,
    fieldsToTranslate?: string[]
  ): Promise<any> {
    // 这里应该调用真正的翻译服务，比如Google Translate API
    // 现在只是简单地复制内容并添加语言标识
    const translated = JSON.parse(JSON.stringify(sourceContent))

    // 如果指定了要翻译的字段，只翻译这些字段
    if (fieldsToTranslate && fieldsToTranslate.length > 0) {
      for (const field of fieldsToTranslate) {
        if (this.hasNestedProperty(translated, field)) {
          const value = this.getNestedProperty(translated, field)
          if (typeof value === 'string' && value.trim()) {
            // 简单的模拟翻译：添加语言标识
            this.setNestedProperty(translated, field, `[${targetLocale}] ${value}`)
          }
        }
      }
    }

    return translated
  }

  private hasNestedProperty(obj: any, path: string): boolean {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined
    }, obj) !== undefined
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key]
    }, obj)
  }

  private setNestedProperty(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {}
      return current[key]
    }, obj)
    target[lastKey] = value
  }

  private async saveTranslationResults(
    contentType: string,
    sourceContentId: string,
    sourceLocale: string,
    translationResults: Record<string, any>
  ): Promise<void> {
    if (contentType.startsWith('ProjectGameLocale_')) {
      await this.saveGameContentTranslations(contentType, sourceContentId, sourceLocale, translationResults)
    } else {
      await this.saveProjectSiteSettingTranslations(contentType, sourceContentId, sourceLocale, translationResults)
    }
  }

  private async saveProjectSiteSettingTranslations(
    contentType: string,
    sourceContentId: string,
    sourceLocale: string,
    translationResults: Record<string, any>
  ): Promise<void> {
    // 获取源内容的项目ID
    const sourceContent = await prisma.projectLocaleSiteSetting.findUnique({
      where: { id: sourceContentId },
      select: { projectId: true, type: true }
    })

    if (!sourceContent) {
      throw new Error(`源内容不存在: ${sourceContentId}`)
    }

    const { projectId, type } = sourceContent

    // 为每个目标语言保存翻译结果
    for (const [locale, content] of Object.entries(translationResults)) {
      await prisma.projectLocaleSiteSetting.upsert({
        where: {
          project_locale_site_setting_unique: {
            projectId,
            locale,
            type
          }
        },
        update: {
          content,
          status: "COMPLETED",
          updatedAt: new Date()
        },
        create: {
          projectId,
          locale,
          type,
          content,
          status: "COMPLETED"
        }
      })
    }
  }

  private async saveGameContentTranslations(
    contentType: string,
    sourceContentId: string,
    sourceLocale: string,
    translationResults: Record<string, any>
  ): Promise<void> {
    // 获取源游戏内容
    const sourceContent = await prisma.projectGameLocale.findUnique({
      where: { id: sourceContentId },
      select: {
        projectId: true,
        gameId: true,
        type: true,
        contentId: true,
        sort: true
      }
    })

    if (!sourceContent) {
      throw new Error(`源游戏内容不存在: ${sourceContentId}`)
    }

    const { projectId, gameId, type, contentId, sort } = sourceContent

    // 为每个目标语言保存翻译结果
    for (const [locale, content] of Object.entries(translationResults)) {
      await prisma.projectGameLocale.upsert({
        where: {
          project_game_locale_content_id_locale_unique: {
            contentId: contentId || `${gameId}_${type}_${locale}`,
            locale
          }
        },
        update: {
          content,
          status: "COMPLETED",
          updatedAt: new Date()
        },
        create: {
          projectId,
          gameId,
          type,
          locale,
          contentId: contentId || `${gameId}_${type}_${locale}`,
          content,
          sort: sort || 0,
          status: "COMPLETED"
        }
      })
    }
  }

  private async updateTaskStatus(
    taskId: string,
    status: string,
    result?: Record<string, any>,
    errorMessage?: string
  ): Promise<void> {
    const updateData: any = {
      status,
      updatedAt: new Date()
    }

    if (status === BACKGROUND_TASK_STATUS.PROCESSING) {
      updateData.startedAt = new Date()
    }

    if (status === BACKGROUND_TASK_STATUS.SUCCESS || status === BACKGROUND_TASK_STATUS.FAILED) {
      updateData.completedAt = new Date()
    }

    if (result) {
      updateData.result = result
    }

    if (errorMessage) {
      updateData.errorMessage = errorMessage
    }

    await prisma.backgroundTask.update({
      where: { id: taskId },
      data: updateData
    })
  }

  private async requeueTask(taskData: TranslationTaskMessage): Promise<void> {
    // 重新发送消息到队列
    // 这里需要使用MessageService来发送消息
    // 由于我们在TaskQueueListener中，可以通过父类的方法来发送
    // 但这需要修改TaskQueueListener的实现，暂时先记录日志
    logger.info(`[TranslationTaskListener] 需要重新入队任务`, { taskId: taskData.taskId })
  }
}
