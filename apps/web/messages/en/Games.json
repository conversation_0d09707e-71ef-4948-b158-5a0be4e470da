{"Games": {"pageTitle": "Game Management", "pageDescription": "Manage your game list", "gameManagement": "Game Management", "addGame": "Add Game", "editGame": "Edit Game: {name}", "editGameTitle": "Edit Game", "editGameDescription": "Edit game information and content", "filterConditions": "Filter Conditions", "searchGameName": "Search game name", "gameCategory": "Game Category", "allCategories": "All Categories", "gameTag": "Game Tag", "allTags": "All Tags", "tagsLabel": "Tags", "status": "Status", "allStatuses": "All Statuses", "selectDateRange": "Select date range", "dateRange": "Date Range", "today": "Today", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "gameList": "Game List", "gameName": "Game Name", "category": "Category", "creationTime": "Creation Time", "operations": "Operations", "actions": "Actions", "categories": {"action": "Action", "adventure": "Adventure", "puzzle": "Puzzle", "rpg": "RPG", "sports": "Sports", "strategy": "Strategy", "simulation": "Simulation", "racing": "Racing", "shooter": "Shooter", "arcade": "Arcade"}, "uncategorized": "Uncategorized", "statuses": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "rejected": "Rejected"}, "edit": "Edit", "preview": "Preview", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this game?", "deleteSuccess": "Game deleted successfully", "deleteError": "Error deleting game", "previewNotImplemented": "Preview feature not implemented yet", "loading": "Loading...", "loadError": "Failed to load game list", "retry": "Retry", "noGames": "No games found", "basicInfo": "Basic Info", "contentModules": "Content Modules", "seoSettings": "SEO Settings", "commentManagement": "Comment Management", "relatedGames": "Related Games", "gameBasicInfo": "Game Basic Information", "enterGameName": "Enter game name", "selectCategory": "Select category", "selectStatus": "Select status", "gameIframeUrl": "Game iframe URL", "enterGameIframeUrl": "Enter game iframe URL", "gameCoverImage": "Game Cover Image", "dragImageHere": "Drag image here or click to upload", "imageRequirements": "Recommended size: 800x600px, max 2MB", "uploadImage": "Upload Image", "replaceImage": "Replace Image", "supportedFormats": "Supported formats", "gameDescription": "Game Description", "enterGameDescription": "Enter game description...", "reset": "Reset", "saving": "Saving...", "saveSettings": "Save Settings", "noGameData": "No game data", "contentModulesNotImplemented": "Content modules feature not implemented yet", "seoSettingsNotImplemented": "SEO settings feature not implemented yet", "commentManagementNotImplemented": "Comment management feature not implemented yet", "relatedGamesNotImplemented": "Related games feature not implemented yet", "gameSlug": "Game Slug", "enterGameSlug": "Enter game slug", "gameSlugDescription": "Game slug can only contain lowercase letters, numbers, and hyphens (-)", "gameCategories": "Game Categories", "selectGameCategories": "Select game categories", "gameType": "Game Type", "selectGameType": "Select game type", "gameTypes": {"iframe": "<PERSON><PERSON><PERSON>", "download": "Download", "popup": "Popup", "placeholder": "Placeholder"}, "gameTags": "Game Tags", "enterTag": "Enter tag", "addTag": "Add Tag", "noTags": "No tags", "userInputText": "User Input Text", "enterUserInputText": "Enter user customizable text content", "userInputTextDescription": "Custom text content that users can input in the game", "isPrimaryGame": "Set as Primary Game", "isPrimaryGameDescription": "Set this game as the primary game for the project, it will be highlighted on the homepage", "gameDownloadSettings": "Game Download Settings", "showDownloadButton": "Show Download Button", "showDownloadButtonDescription": "Display download button on the game page", "pcDownloadLink": "PC Download Link", "macDownloadLink": "Mac Download Link", "androidDownloadLink": "Android Download Link", "iosDownloadLink": "iOS Download Link", "steamDownloadLink": "Steam Download Link", "enterDownloadLink": "Enter download link", "backgroundSettings": "Background Settings", "showBackground": "Show Background", "showBackgroundDescription": "Display custom background in the game area", "backgroundType": "Background Type", "backgroundTypeImage": "Image Background", "backgroundTypeVideo": "Video Background", "backgroundImage": "Background Image", "backgroundVideo": "Background Video", "backgroundVideoDescription": "Enter video URL, supports MP4 format or YouTube embed links", "relatedVideos": "Related Videos", "addRelatedVideo": "Add Related Video", "enterRelatedVideoUrl": "Enter related video URL", "noRelatedVideos": "No related videos", "tags": {"popular": "Popular", "new": "New", "featured": "Featured", "trending": "Trending"}, "metaInformation": "Meta Information", "metaInformationDescription": "Set the title and description displayed in search engines", "metaTitle": "Meta Title", "metaName": "Game Name", "metaSlogan": "<PERSON> Slogan", "enterMetaTitle": "Enter meta title", "enterMetaName": "Enter game name, leave empty to not internationalize the name", "enterMetaSlogan": "Enter game slogan or tagline", "metaDescription": "Meta Description", "enterMetaDescription": "Enter meta description", "canonicalUrl": "Canonical URL", "enterCanonicalUrl": "Enter canonical URL", "canonicalUrlDescription": "Canonical URL helps avoid duplicate content issues, usually the main access address of the game", "characters": "Characters", "recommendedMax": "Recommended max", "keywords": "Keywords", "keywordsDescription": "Add keywords related to the game to help search engines better understand the content", "enterKeyword": "Enter keyword", "addKeyword": "Add Keyword", "noKeywords": "No keywords", "keywordsTip": "Keywords Tip", "keywordsTipDescription": "Keywords should be relevant to the game content. It's recommended to add 3-5 keywords and use them naturally in the game description", "socialMediaSharing": "Social Media Sharing", "socialMediaSharingDescription": "Set the information displayed when sharing the game on social media", "openGraph": "Open Graph", "ogTitle": "OG Title", "ogTitleDescription": "Title displayed when sharing on social media like Facebook", "enterOgTitle": "Enter OG title", "ogDescription": "OG Description", "ogDescriptionInfo": "Description displayed when sharing on social media like Facebook", "enterOgDescription": "Enter OG description", "ogImage": "OG Image", "ogImageDescription": "Image URL displayed when sharing on social media like Facebook", "enterOgImageUrl": "Enter OG image URL", "twitterSection": "Twitter", "twitterCard": "Twitter Card Type", "twitterCardDescription": "Type of Twitter card, such as summary, summary_large_image, etc.", "enterTwitterCard": "Enter Twitter card type", "twitterTitle": "Twitter Title", "twitterTitleDescription": "Title displayed when sharing on Twitter", "enterTwitterTitle": "Enter Twitter title", "twitterDescription": "Twitter Description", "twitterDescriptionInfo": "Description displayed when sharing on Twitter", "enterTwitterDescription": "Enter Twitter description", "twitterImage": "Twitter Image", "twitterImageDescription": "Image URL displayed when sharing on Twitter", "enterTwitterImageUrl": "Enter Twitter image URL", "seoAnalysis": "SEO Analysis", "seoAnalysisDescription": "Analyze the completeness of game SEO settings and provide optimization suggestions", "seoScore": "SEO Score", "seoScoreDescription": "Score based on the completeness of meta title, meta description, and keywords", "seoSuggestions": "SEO Suggestions", "noSeoSuggestions": "No SEO suggestions", "seoTitleTooShort": "Meta title is too short, 30-60 characters recommended", "seoTitleTooLong": "Meta title is too long, no more than 60 characters recommended", "seoTitleGoodLength": "Meta title length is appropriate", "seoTitleContainsKeyword": "Meta title contains keywords, good for SEO", "seoTitleMissingKeyword": "Meta title does not contain keywords, consider adding some", "seoTitleMissing": "Missing meta title, which is very unfavorable for SEO", "seoDescriptionTooShort": "Meta description is too short, 80-160 characters recommended", "seoDescriptionTooLong": "Meta description is too long, no more than 160 characters recommended", "seoDescriptionGoodLength": "Meta description length is appropriate", "seoDescriptionContainsKeyword": "Meta description contains keywords, good for SEO", "seoDescriptionMissingKeyword": "Meta description does not contain keywords, consider adding some", "seoDescriptionMissing": "Missing meta description, which is very unfavorable for SEO", "seoTooFewKeywords": "Too few keywords, 3-5 keywords recommended", "seoTooManyKeywords": "Too many keywords, 3-5 keywords recommended", "seoGoodKeywordCount": "Keyword count is appropriate", "seoKeywordsMissing": "Missing keywords, which is very unfavorable for SEO", "seoCanonicalUrlMissing": "Missing canonical URL, which may cause duplicate content issues", "relatedGamesSettings": "Related Games Settings", "relatedGamesDescription": "Set up recommendations for games related to this one", "recommendMode": "Recommendation Mode", "recommendModeDescription": "Choose between manual selection or automatic recommendations", "manual": "Manual", "auto": "Auto", "manualSelection": "Manual Selection", "autoRecommendation": "Auto Recommendation", "selectGames": "Select Games", "selectedGames": "Selected Games", "autoRecommendationNote": "Auto Recommendation Note", "autoRecommendationDescription": "The system will automatically recommend related games based on the following rules, which you can adjust as needed", "sameCategoryRule": "Same Category Rule", "sameTagsRule": "Same Tags Rule", "maxAutoItems": "Maximum Recommendation Count", "translateToOtherLanguages": "Translate to Other Languages", "translationSuccess": "Translation Successful", "translationError": "Translation Failed", "searchGames": "Search Games", "relationType": "Relation Type", "similarGames": "Similar Games", "seriesGames": "Series Games", "recommendedGames": "Recommended Games", "noGamesFound": "No Games Found", "noRelatedGames": "No Related Games", "order": "Order", "translation": {"title": "AI Translation", "description": "Select target languages for automatic translation. The system will use AI to translate related game content", "selectAll": "Select All", "cancel": "Cancel", "start": "Start Translation", "translating": "Translating...", "inProgress": "Translating related game content, please wait...", "timeEstimate": "The translation process may take a few minutes, depending on the amount and complexity of content", "selectLanguageError": "Please select at least one target language", "translationError": "Translation failed, please try again later"}, "saveChanges": "Save Changes", "moduleTypes": {"gameplay": "Gameplay", "gameplayDescription": "Introduce the gameplay and features", "strategy": "Strategy", "strategyDescription": "Provide game strategies and tips", "screenshots": "Screenshots", "screenshotsDescription": "Show game screenshots", "videos": "Videos", "videosDescription": "Show game videos", "faq": "FAQ", "faqDescription": "Answer frequently asked questions about the game", "download": "Download", "downloadDescription": "Provide download links for the game", "updates": "Updates", "updatesDescription": "Record game update history", "community": "Community", "communityDescription": "Provide information about the game community"}}}