/**
 * ImageUploader 组件主导出文件
 */

// 主组件
export { ImageUploader } from "./ImageUploader"
export { ImageUploader as default } from "./ImageUploader"

// 子组件（可选导出，用于高级定制）
export { UploadButton } from "./components/UploadButton"
export { UploadArea } from "./components/UploadArea"
export { PreviewArea } from "./components/PreviewArea"
export { PreviewModal } from "./components/PreviewModal"

// Hooks（可选导出，用于自定义实现）
export { useImageUploader } from "./hooks/useImageUploader"
export { usePreviewModal } from "./hooks/usePreviewModal"

// 类型定义
export type {
  ImageUploaderProps,
  ImageUploaderVariant,
  ImageUploaderSize,
  ImageUploaderVariantWithLegacy,
  UploadState,
  PreviewModalState,
  UploadOptions,
  UploadResult,
  UploadButtonProps,
  UploadAreaProps,
  PreviewAreaProps,
  PreviewModalProps,
} from "./types"

// 常量（可选导出，用于自定义配置）
export {
  imageUploaderVariants,
  DEFAULT_ACCEPT,
  DEFAULT_VARIANT,
  DEFAULT_SIZE,
  SUPPORTED_FORMATS,
} from "./constants"

// 工具函数（可选导出，用于自定义实现）
export {
  isVideoFile,
  generateFilePath,
  generateFileName,
  getSignedUrl,
  uploadWithProgress,
  buildFileUrl,
  uploadFile,
  normalizeVariant,
  getUploadIconSize,
  getLoadingIconSize,
  shouldShowFormatHint,
  shouldShowText,
} from "./utils"
