/**
 * ImageUploader 组件常量定义
 */

import { cva } from "class-variance-authority"

// 组件样式变体配置
export const imageUploaderVariants = cva("", {
  variants: {
    variant: {
      default: "",
      image: "",
      avatar: "",
      cover: "",
    },
    size: {
      xs: "",
      md: "",
      lg: "",
      xl: "",
    },
  },
  compoundVariants: [
    // Image variant sizes - 正方形，适合通用图片上传
    {
      variant: "image",
      size: "xs",
      class: "h-[80px] w-[80px] min-h-[80px] min-w-[80px]",
    },
    {
      variant: "image",
      size: "md",
      class: "h-[120px] w-[120px] min-h-[120px] min-w-[120px]",
    },
    {
      variant: "image",
      size: "lg",
      class: "h-[160px] w-[160px] min-h-[160px] min-w-[160px]",
    },
    {
      variant: "image",
      size: "xl",
      class: "h-[200px] w-[200px] min-h-[200px] min-w-[200px]",
    },
    // Avatar variant sizes - 圆形头像，尺寸适中
    {
      variant: "avatar",
      size: "xs",
      class: "h-[48px] w-[48px] min-h-[48px] min-w-[48px] rounded-full",
    },
    {
      variant: "avatar",
      size: "md",
      class: "h-[80px] w-[80px] min-h-[80px] min-w-[80px] rounded-full",
    },
    {
      variant: "avatar",
      size: "lg",
      class: "h-[120px] w-[120px] min-h-[120px] min-w-[120px] rounded-full",
    },
    {
      variant: "avatar",
      size: "xl",
      class: "h-[160px] w-[160px] min-h-[160px] min-w-[160px] rounded-full",
    },
    // Cover variant sizes - 16:9 宽屏比例，适合封面图片
    {
      variant: "cover",
      size: "xs",
      class: "h-[60px] w-[107px] min-h-[60px] min-w-[107px]",
    },
    {
      variant: "cover",
      size: "md",
      class: "h-[90px] w-[160px] min-h-[90px] min-w-[160px]",
    },
    {
      variant: "cover",
      size: "lg",
      class: "h-[120px] w-[213px] min-h-[120px] min-w-[213px]",
    },
    {
      variant: "cover",
      size: "xl",
      class: "h-[150px] w-[267px] min-h-[150px] min-w-[267px]",
    },
  ],
  defaultVariants: {
    variant: "default",
    size: "md",
  },
})

// 默认配置
export const DEFAULT_ACCEPT = "image/*"
export const DEFAULT_VARIANT = "default"
export const DEFAULT_SIZE = "md"

// 上传配置
export const UPLOAD_TIMEOUT = 30000 // 30秒超时
export const PROGRESS_RESET_DELAY = 1000 // 进度条重置延迟

// 文件类型检测
export const VIDEO_MIME_TYPES = ["video/"]
export const IMAGE_MIME_TYPES = ["image/"]

// 支持的文件格式提示
export const SUPPORTED_FORMATS = {
  image: "JPG、PNG、WEBP",
  video: "MP4、MOV、AVI",
}

// 环境变量键名
export const ENV_KEYS = {
  R2_PUBLIC_DOMAIN: "R2_PUBLIC_DOMAIN",
} as const
