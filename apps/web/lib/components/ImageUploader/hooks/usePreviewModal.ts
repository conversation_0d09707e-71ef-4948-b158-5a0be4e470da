/**
 * 预览模态框状态管理 Hook
 */

import { useState, useCallback } from "react"
import type { PreviewModalState } from "../types"

export function usePreviewModal(): PreviewModalState {
  const [isOpen, setIsOpen] = useState(false)

  const onOpen = useCallback(() => {
    setIsOpen(true)
  }, [])

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [])

  return {
    isOpen,
    onOpen,
    onClose,
  }
}
