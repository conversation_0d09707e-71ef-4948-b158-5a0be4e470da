/**
 * ImageUploader 状态管理 Hook
 */

import { useState, useEffect, useCallback } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import type { UploadState } from "../types"
import { uploadFile, isVideoFile } from "../utils"
import { PROGRESS_RESET_DELAY } from "../constants"

interface UseImageUploaderOptions {
  value?: string
  onChange?: (url: string) => void
  projectId: string
  accept: string
}

interface UseImageUploaderReturn extends UploadState {
  handleUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>
  handleClear: () => void
  isVideo: boolean
}

export function useImageUploader({
  value,
  onChange,
  projectId,
  accept,
}: UseImageUploaderOptions): UseImageUploaderReturn {
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(value)
  const t = useTranslations("Editor")

  const isVideo = isVideoFile(accept)

  // 当value属性变化时更新预览URL
  useEffect(() => {
    // 处理空值：空字符串或undefined都设置为undefined
    if (!value || value.trim() === "") {
      setPreviewUrl(undefined)
    } else {
      setPreviewUrl(value)
    }
  }, [value])

  // 上传处理函数
  const handleUpload = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (!file) return

      try {
        setLoading(true)
        setProgress(0)

        // 上传文件
        const result = await uploadFile(file, {
          projectId,
          isVideo,
          onProgress: setProgress,
        })

        // 更新预览和值
        setPreviewUrl(result.url)

        // 只有当URL真正变化时才调用onChange
        if (onChange && result.url) {
          onChange(result.url)
        }

        toast.success(t("uploadSuccess") || "上传成功")
      } catch (error) {
        console.error("上传失败:", error)
        const errorMessage =
          error instanceof Error ? error.message : String(error)
        toast.error(t("uploadFailed") || `上传失败: ${errorMessage}`)
      } finally {
        setLoading(false)
        // 使用RAF代替setTimeout，更加浏览器友好
        requestAnimationFrame(() => {
          setTimeout(() => setProgress(0), PROGRESS_RESET_DELAY)
        })
      }
    },
    [projectId, isVideo, onChange, t],
  )

  // 清除处理函数
  const handleClear = useCallback(() => {
    setPreviewUrl(undefined)
    if (onChange) {
      onChange("")
    }
  }, [onChange])

  return {
    loading,
    progress,
    previewUrl,
    handleUpload,
    handleClear,
    isVideo,
  }
}
