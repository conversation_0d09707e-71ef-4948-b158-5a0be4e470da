/**
 * ImageUploader 工具函数
 */

import type { UploadOptions, UploadResult, ImageUploaderVariant } from "./types"

/**
 * 检查是否为视频文件
 */
export function isVideoFile(accept: string): boolean {
	return accept.startsWith("video/")
}

/**
 * 生成文件路径
 */
export function generateFilePath(projectId: string, isVideo: boolean): string {
	const currentDate = new Date().toISOString().split("T")[0]
	return `${projectId}/${currentDate}/${isVideo ? "videos" : "images"}`
}

/**
 * 生成文件名
 */
export function generateFileName(file: File, basePath: string): string {
	const fileExt = file.name.split(".").pop() || "jpg"
	const timestamp = Date.now()
	const randomStr = Math.random().toString(36).substring(7)
	return `${basePath}/${timestamp}-${randomStr}.${fileExt}`
}

/**
 * 获取签名URL
 */
export async function getSignedUrl(fileName: string): Promise<string> {
	console.log("正在获取签名URL...")
	const res = await fetch(`/api/s3/put-signed-url?key=${fileName}`)

	if (!res.ok) {
		const errorText = await res.text()
		throw new Error(`获取签名URL失败: ${res.status} ${errorText}`)
	}

	const data = await res.json()

	if (!data.url) {
		console.error("API响应缺少URL:", data)
		throw new Error("API响应缺少URL")
	}

	return data.url
}

/**
 * 带进度的文件上传
 */
export function uploadWithProgress(
	url: string,
	file: File,
	onProgress: (percent: number) => void,
): Promise<void> {
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest()
		xhr.open("PUT", url, true)

		// 设置Content-Type头
		xhr.setRequestHeader(
			"Content-Type",
			file.type || "application/octet-stream",
		)

		// 打印完整的URL用于调试
		console.log("正在上传到URL:", url)
		console.log("文件类型:", file.type)

		xhr.upload.onprogress = (event) => {
			if (event.lengthComputable) {
				const percent = (event.loaded / event.total) * 100
				onProgress(Math.round(percent))
			}
		}

		xhr.onload = () => {
			if (xhr.status >= 200 && xhr.status < 300) {
				console.log("上传成功:", xhr.status)
				resolve()
			} else {
				console.error("上传失败，状态码:", xhr.status, xhr.statusText)
				// 尝试读取响应内容
				try {
					console.error("响应内容:", xhr.responseText)
				} catch (e) {
					console.error("无法读取响应内容")
				}
				reject(
					new Error(`状态码 ${xhr.status}: ${xhr.statusText || "未知错误"}`),
				)
			}
		}

		xhr.onerror = (e) => {
			console.error("上传过程中发生XHR错误:", e)
			reject(new Error(`网络错误: ${xhr.statusText || "未知错误"}`))
		}

		xhr.send(file)
	})
}

/**
 * 构建文件URL
 */
export function buildFileUrl(fileName: string): string {
	return `${process.env.R2_PUBLIC_DOMAIN}/${fileName}`
}

/**
 * 完整的文件上传流程
 */
export async function uploadFile(
	file: File,
	options: UploadOptions,
): Promise<UploadResult> {
	const { projectId, isVideo, onProgress } = options

	// 生成文件路径和名称
	const basePath = generateFilePath(projectId, isVideo)
	const fileName = generateFileName(file, basePath)

	// 获取签名URL
	const signedUrl = await getSignedUrl(fileName)

	console.log("已获取签名URL，开始上传...")

	// 上传文件
	await uploadWithProgress(signedUrl, file, onProgress)

	console.log("上传完成")

	// 构建文件URL
	const fileUrl = buildFileUrl(fileName)

	return {
		url: fileUrl,
		fileName,
	}
}

/**
 * 向后兼容：将 "button" variant 映射到 "default"
 */
export function normalizeVariant(variant?: string): ImageUploaderVariant {
	return variant === "button"
		? "default"
		: (variant as ImageUploaderVariant) || "default"
}

/**
 * 获取上传图标大小
 */
export function getUploadIconSize(
	variant: ImageUploaderVariant,
	size: string,
): number {
	// 根据 variant 和 size 组合确定图标大小
	if (variant === "avatar") {
		switch (size) {
			case "xs":
				return 16
			case "md":
				return 20
			case "lg":
				return 24
			case "xl":
				return 28
			default:
				return 16
		}
	}

	// 其他 variant 的图标大小
	switch (size) {
		case "xs":
			return 14
		case "md":
			return 18
		case "lg":
			return 22
		case "xl":
			return 26
		default:
			return 18
	}
}

/**
 * 获取加载图标大小
 */
export function getLoadingIconSize(
	variant: ImageUploaderVariant,
	size: string,
): number {
	// 加载图标通常比上传图标稍大一些
	if (variant === "avatar") {
		switch (size) {
			case "xs":
				return 16
			case "md":
				return 20
			case "lg":
				return 24
			case "xl":
				return 28
			default:
				return 20
		}
	}

	// 其他 variant 的加载图标大小
	switch (size) {
		case "xs":
			return 18
		case "md":
			return 22
		case "lg":
			return 26
		case "xl":
			return 30
		default:
			return 22
	}
}

/**
 * 检查是否应该显示格式提示
 */
export function shouldShowFormatHint(
	variant: ImageUploaderVariant,
	size: string,
): boolean {
	// xs 尺寸和 avatar 的 xs/md 不显示格式提示，避免文字挤压
	if (size === "xs") return false
	if (variant === "avatar" && (size === "xs" || size === "md")) return false
	return true
}

/**
 * 检查是否应该显示文本
 */
export function shouldShowText(
	_variant: ImageUploaderVariant,
	size: string,
): boolean {
	// xs 尺寸下所有 variant 都不显示文本，避免文字挤压
	if (size === "xs") return false

	// 其他尺寸正常显示文本
	return true
}
