/**
 * ImageUploader 组件类型定义
 */

export type ImageUploaderVariant = "default" | "image" | "avatar" | "cover"
export type ImageUploaderSize = "xs" | "md" | "lg" | "xl"

// 向后兼容：支持原有的 "button" variant
export type ImageUploaderVariantWithLegacy = ImageUploaderVariant | "button"

export interface ImageUploaderProps {
  value?: string
  onChange?: (url: string) => void
  projectId: string
  accept?: string
  variant?: ImageUploaderVariantWithLegacy
  size?: ImageUploaderSize
}

export interface UploadState {
  loading: boolean
  progress: number
  previewUrl?: string
}

export interface PreviewModalState {
  isOpen: boolean
  onOpen: () => void
  onClose: () => void
}

export interface UploadOptions {
  projectId: string
  isVideo: boolean
  onProgress: (progress: number) => void
}

export interface UploadResult {
  url: string
  fileName: string
}

// 内部组件 props 类型
export interface UploadButtonProps {
  loading: boolean
  isVideo: boolean
  accept: string
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  progress: number
}

export interface UploadAreaProps {
  variant: ImageUploaderVariant
  size: ImageUploaderSize
  loading: boolean
  isVideo: boolean
  accept: string
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  hasPreview: boolean
}

export interface PreviewAreaProps {
  variant: ImageUploaderVariant
  size: ImageUploaderSize
  previewUrl: string
  isVideo: boolean
  onPreview: () => void
  onClear: () => void
}

export interface PreviewModalProps {
  isOpen: boolean
  onClose: () => void
  previewUrl?: string
  isVideo: boolean
}
