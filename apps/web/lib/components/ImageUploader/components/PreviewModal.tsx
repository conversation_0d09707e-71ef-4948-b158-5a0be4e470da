/**
 * 预览模态框组件
 */

"use client"

import React, { useState } from "react"
import { Button, Dialog, DialogContent, DialogTitle } from "@repo/ui/components"
import { X, AlertCircle } from "lucide-react"
import type { PreviewModalProps } from "../types"

export function PreviewModal({
  isOpen,
  onClose,
  previewUrl,
  isVideo,
}: PreviewModalProps) {
  const [imageError, setImageError] = useState(false)
  const [videoError, setVideoError] = useState(false)

  // 检查空值：undefined、null 或空字符串都不渲染
  if (!previewUrl || previewUrl.trim() === "") {
    return null
  }

  const handleImageError = () => {
    console.warn("图片加载失败:", previewUrl)
    setImageError(true)
  }

  const handleVideoError = () => {
    console.warn("视频加载失败:", previewUrl)
    setVideoError(true)
  }

  const renderFallback = () => (
    <div className="flex flex-col items-center justify-center p-12 text-muted-foreground">
      <AlertCircle size={48} className="mb-4" />
      <p className="text-lg font-medium mb-2">
        {isVideo ? "视频加载失败" : "图片加载失败"}
      </p>
      <p className="text-sm text-center max-w-md">
        文件可能已被删除或网络连接出现问题，请稍后重试
      </p>
    </div>
  )

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-5xl p-0" aria-describedby="preview-description">
        <DialogTitle className="sr-only">
          {isVideo ? "视频预览" : "图片预览"}
        </DialogTitle>
        <div id="preview-description" className="sr-only">
          {isVideo ? "全屏视频预览模态框" : "全屏图片预览模态框"}
        </div>
        <div className="relative flex items-center justify-center bg-background rounded-lg overflow-hidden">
          <div className="absolute top-2 right-2 z-10">
            <Button
              size="sm"
              variant="destructive"
              onClick={onClose}
              className="h-8 w-8 p-0 shadow-lg"
              aria-label="关闭预览"
            >
              <X size={16} />
            </Button>
          </div>

          {isVideo ? (
            videoError ? (
              renderFallback()
            ) : (
              <video
                src={previewUrl}
                controls
                className="w-full h-auto max-h-[80vh] rounded-lg"
                muted
                onError={handleVideoError}
                onLoadStart={() => setVideoError(false)}
              />
            )
          ) : (
            imageError ? (
              renderFallback()
            ) : (
              <img
                src={previewUrl}
                alt="预览图片"
                className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
                onError={handleImageError}
                onLoad={() => setImageError(false)}
              />
            )
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
