/**
 * 默认按钮上传组件
 */

"use client"

import React from "react"
import { Button, Progress } from "@repo/ui/components"
import { Upload } from "lucide-react"
import { useTranslations } from "next-intl"
import { CircleLoading } from "../../CircleLoading"
import type { UploadButtonProps } from "../types"

export function UploadButton({
  loading,
  isVideo,
  accept,
  onUpload,
  progress,
}: UploadButtonProps) {
  const t = useTranslations("Editor")

  return (
    <>
      <Button
        asChild
        variant="outline"
        disabled={loading}
        className={`cursor-pointer transition-all duration-200 hover:shadow-md active:scale-[0.98] ${
          loading ? "pointer-events-none" : ""
        }`}
      >
        <label className="flex items-center gap-2">
          {loading ? (
            <>
              <CircleLoading size={16} showText={false} />
              <span className="animate-pulse">
                {isVideo ? "正在上传视频..." : "正在上传图片..."}
              </span>
            </>
          ) : (
            <>
              <div className="p-1 rounded bg-primary/10">
                <Upload size={16} className="text-primary" />
              </div>
              <span className="font-medium">
                {t(isVideo ? "uploadVideo" : "uploadImage")}
              </span>
            </>
          )}
          <input
            type="file"
            className="hidden"
            accept={accept}
            onChange={onUpload}
            disabled={loading}
            aria-label={t(isVideo ? "uploadVideo" : "uploadImage")}
          />
        </label>
      </Button>

      {progress > 0 && (
        <div className="flex-1 max-w-[200px] animate-in slide-in-from-left duration-300">
          <div className="space-y-2 p-3 bg-muted/50 rounded-lg border border-border/50">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-muted-foreground">
                {isVideo ? "视频上传中" : "图片上传中"}
              </span>
              <span className="text-xs font-bold text-primary">
                {progress}%
              </span>
            </div>
            <Progress
              value={progress}
              className="h-2 bg-muted"
            />
          </div>
        </div>
      )}
    </>
  )
}
