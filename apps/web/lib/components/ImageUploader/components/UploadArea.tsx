/**
 * 图片区域上传组件
 */

"use client"

import React from "react"
import { Upload, User2 } from "lucide-react"
import { useTranslations } from "next-intl"
import { CircleLoading } from "../../CircleLoading"
import { imageUploaderVariants, SUPPORTED_FORMATS } from "../constants"
import {
	getUploadIconSize,
	getLoadingIconSize,
	shouldShowFormatHint,
	shouldShowText,
} from "../utils"
import type { UploadAreaProps } from "../types"
import { cn } from "@repo/utils/react"

export function UploadArea({
	variant,
	size,
	loading,
	isVideo,
	accept,
	onUpload,
	hasPreview,
}: UploadAreaProps) {
	const t = useTranslations("Editor")

	// 如果已有预览，不显示上传区域
	if (hasPreview) {
		return null
	}

	const uploadIconSize = getUploadIconSize(variant, size)
	const loadingIconSize = getLoadingIconSize(variant, size)
	const showFormatHint = shouldShowFormatHint(variant, size)
	const showText = shouldShowText(variant, size)

	// 根据尺寸调整内边距和间距
	const getPadding = () => {
		switch (size) {
			case "xs":
				return "p-2"
			case "md":
				return "p-4"
			case "lg":
				return "p-6"
			case "xl":
				return "p-8"
			default:
				return "p-4"
		}
	}

	const getGap = () => {
		switch (size) {
			case "xs":
				return "gap-1"
			case "md":
				return "gap-2"
			case "lg":
				return "gap-3"
			case "xl":
				return "gap-4"
			default:
				return "gap-2"
		}
	}

	const getTextSize = () => {
		switch (size) {
			case "xs":
				return "text-xs"
			case "md":
				return "text-sm"
			case "lg":
				return "text-base"
			case "xl":
				return "text-lg"
			default:
				return "text-sm"
		}
	}

	const getHintTextSize = () => {
		switch (size) {
			case "xs":
				return "text-[10px]"
			case "md":
				return "text-xs"
			case "lg":
				return "text-sm"
			case "xl":
				return "text-base"
			default:
				return "text-xs"
		}
	}

	// 根据 variant 决定边框圆角
	const getBorderRadius = () => {
		return variant === "avatar" ? "" : "rounded-lg"
	}

	return (
		<label
			className={`${imageUploaderVariants({ variant, size })} flex flex-col items-center justify-center ${getGap()} border-2 border-dashed border-border/60 ${getBorderRadius()} ${getPadding()} cursor-pointer transition-all duration-200 hover:border-primary hover:text-primary hover:bg-primary/5 hover:shadow-sm active:scale-[0.98] ${
				loading ? "pointer-events-none opacity-75" : ""
			}`}
		>
			{loading ? (
				<div className={`flex flex-col items-center ${getGap()}`}>
					<CircleLoading size={loadingIconSize} text="上传中..." />
				</div>
			) : (
				<>
					<div
						className={cn(
							size === "xs" ? "p-1" : "p-2",
							"rounded-full bg-primary/10 border border-primary/20 transition-colors duration-200 group-hover:bg-primary/20 flex-shrink-0",
							variant === "avatar" ? "bg-transparent  border-none" : "",
						)}
					>
						{variant === "avatar" ? (
							<User2 size={uploadIconSize} className="text-primary" />
						) : (
							<Upload size={uploadIconSize} className="text-primary" />
						)}
					</div>
					{showText && variant !== "avatar" && (
						<span
							className={`${getTextSize()} text-center font-medium whitespace-nowrap`}
						>
							{t(isVideo ? "uploadVideo" : "uploadImage")}
						</span>
					)}
					{showFormatHint && (
						<span
							className={`${getHintTextSize()} text-muted-foreground text-center whitespace-nowrap hidden sm:hidden`}
						>
							{t("supportedFormats") || "支持格式"}:{" "}
							{SUPPORTED_FORMATS[isVideo ? "video" : "image"]}
						</span>
					)}
				</>
			)}
			<input
				type="file"
				className="hidden"
				accept={accept}
				onChange={onUpload}
				disabled={loading}
				aria-label={t(isVideo ? "uploadVideo" : "uploadImage")}
			/>
		</label>
	)
}
