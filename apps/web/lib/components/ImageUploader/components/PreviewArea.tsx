/**
 * 预览区域组件
 */

"use client"

import React, { useState } from "react"
import { Button } from "@repo/ui/components"
import { Eye, X, ImageIcon, AlertCircle, Play } from "lucide-react"
import { imageUploaderVariants } from "../constants"
import type { PreviewAreaProps } from "../types"

export function PreviewArea({
  variant,
  size,
  previewUrl,
  isVideo,
  onPreview,
  onClear,
}: PreviewAreaProps) {
  const [imageError, setImageError] = useState(false)
  const [videoError, setVideoError] = useState(false)

  // 检查空值：undefined、null 或空字符串都不渲染
  if (!previewUrl || previewUrl.trim() === "") {
    return null
  }

  const handleImageError = () => {
    console.warn("图片加载失败:", previewUrl)
    setImageError(true)
  }

  const handleVideoError = () => {
    console.warn("视频加载失败:", previewUrl)
    setVideoError(true)
  }

  const containerClassName = `relative overflow-hidden bg-gradient-to-br from-muted/50 to-muted flex items-center justify-center group border border-border/50 shadow-sm hover:shadow-md transition-all duration-200 ${
    variant === "avatar" ? imageUploaderVariants({ variant, size }) :
    variant === "cover" ? imageUploaderVariants({ variant, size }) :
    "max-w-[400px] max-h-[300px] rounded-lg"
  }`

  const mediaClassName = `w-full h-full object-cover transition-transform duration-200 group-hover:scale-105 ${
    variant === "avatar" ? "rounded-full" : "rounded-lg"
  }`

  // 根据尺寸调整fallback样式
  const getFallbackIconSize = () => {
    switch (size) {
      case "xs": return 16
      case "md": return 24
      case "lg": return 32
      case "xl": return 40
      default: return 24
    }
  }

  const getFallbackPadding = () => {
    switch (size) {
      case "xs": return "p-2"
      case "md": return "p-4"
      case "lg": return "p-6"
      case "xl": return "p-8"
      default: return "p-4"
    }
  }

  const getFallbackTextSize = () => {
    switch (size) {
      case "xs": return "text-[10px]"
      case "md": return "text-xs"
      case "lg": return "text-sm"
      case "xl": return "text-base"
      default: return "text-xs"
    }
  }

  const getButtonSize = () => {
    switch (size) {
      case "xs": return { buttonClass: "h-5 w-5 p-0", iconSize: 10 }
      case "md": return { buttonClass: "h-7 w-7 p-0", iconSize: 14 }
      case "lg": return { buttonClass: "h-8 w-8 p-0", iconSize: 16 }
      case "xl": return { buttonClass: "h-9 w-9 p-0", iconSize: 18 }
      default: return { buttonClass: "h-7 w-7 p-0", iconSize: 14 }
    }
  }

  const renderFallback = () => (
    <div className={`flex flex-col items-center justify-center ${getFallbackPadding()} text-muted-foreground`}>
      <div className="relative">
        {isVideo ? (
          <div className={`${size === "xs" ? "p-2" : "p-3"} rounded-full bg-destructive/10 border border-destructive/20`}>
            <Play size={getFallbackIconSize()} className="text-destructive" />
          </div>
        ) : (
          <div className={`${size === "xs" ? "p-2" : "p-3"} rounded-full bg-destructive/10 border border-destructive/20`}>
            <ImageIcon size={getFallbackIconSize()} className="text-destructive" />
          </div>
        )}
        <div className="absolute -top-1 -right-1">
          <AlertCircle size={size === "xs" ? 8 : 12} className="text-destructive" />
        </div>
      </div>
      {(variant !== "avatar" || size !== "xs") && size !== "xs" && (
        <p className={`${getFallbackTextSize()} text-center mt-2 font-medium whitespace-nowrap`}>
          {isVideo ? "视频加载失败" : "图片加载失败"}
        </p>
      )}
    </div>
  )

  const buttonConfig = getButtonSize()

  return (
    <div className={containerClassName}>
      {/* 操作按钮 */}
      <div className={`absolute ${size === "xs" ? "top-1 right-1" : "top-2 right-2"} z-20 flex ${size === "xs" ? "gap-0.5" : "gap-1"} opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-y-1 group-hover:translate-y-0`}>
        <Button
          size="sm"
          variant="outline"
          onClick={onPreview}
          className={`${buttonConfig.buttonClass} bg-background/90 backdrop-blur-sm border-border/50 hover:bg-background shadow-lg hover:shadow-xl transition-all duration-200`}
          aria-label={isVideo ? "预览视频" : "预览图片"}
        >
          <Eye size={buttonConfig.iconSize} />
        </Button>
        <Button
          size="sm"
          variant="destructive"
          onClick={onClear}
          className={`${buttonConfig.buttonClass} bg-destructive/90 backdrop-blur-sm hover:bg-destructive shadow-lg hover:shadow-xl transition-all duration-200`}
          aria-label="删除文件"
        >
          <X size={buttonConfig.iconSize} />
        </Button>
      </div>

      {/* 媒体内容 */}
      {isVideo ? (
        videoError ? (
          renderFallback()
        ) : (
          <video
            src={previewUrl}
            className={mediaClassName}
            muted
            onError={handleVideoError}
            onLoadStart={() => setVideoError(false)}
            poster="" // 移除默认海报
          />
        )
      ) : (
        imageError ? (
          renderFallback()
        ) : (
          <img
            src={previewUrl}
            alt="预览图片"
            className={mediaClassName}
            onError={handleImageError}
            onLoad={() => setImageError(false)}
          />
        )
      )}

      {/* 加载状态指示器 */}
      {!imageError && !videoError && (
        <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
      )}
    </div>
  )
}
