/**
 * ImageUploader 主组件
 */

"use client"

import React from "react"
import { UploadButton } from "./components/UploadButton"
import { UploadArea } from "./components/UploadArea"
import { PreviewArea } from "./components/PreviewArea"
import { PreviewModal } from "./components/PreviewModal"
import { useImageUploader } from "./hooks/useImageUploader"
import { usePreviewModal } from "./hooks/usePreviewModal"
import { normalizeVariant } from "./utils"
import { DEFAULT_ACCEPT, DEFAULT_VARIANT, DEFAULT_SIZE } from "./constants"
import type { ImageUploaderProps } from "./types"

export function ImageUploader({
  value,
  onChange,
  projectId,
  accept = DEFAULT_ACCEPT,
  variant = DEFAULT_VARIANT,
  size = DEFAULT_SIZE,
}: ImageUploaderProps) {
  // 向后兼容：将 "button" variant 映射到 "default"
  const normalizedVariant = normalizeVariant(variant)

  // 上传状态管理
  const {
    loading,
    progress,
    previewUrl,
    handleUpload,
    handleClear,
    isVideo,
  } = useImageUploader({
    value,
    onChange,
    projectId,
    accept,
  })

  // 预览模态框状态管理
  const { isOpen, onOpen, onClose } = usePreviewModal()

  const hasPreview = Boolean(previewUrl && previewUrl.trim() !== "")

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-2">
        {/* 默认按钮形式上传 */}
        {normalizedVariant === "default" && (
          <UploadButton
            loading={loading}
            isVideo={isVideo}
            accept={accept}
            onUpload={handleUpload}
            progress={progress}
          />
        )}

        {/* 图片区域上传 */}
        {(normalizedVariant === "image" || normalizedVariant === "avatar" || normalizedVariant === "cover") && (
          <UploadArea
            variant={normalizedVariant}
            size={size}
            loading={loading}
            isVideo={isVideo}
            accept={accept}
            onUpload={handleUpload}
            hasPreview={hasPreview}
          />
        )}
      </div>

      {/* 预览区域 */}
      {hasPreview && (
        <PreviewArea
          variant={normalizedVariant}
          size={size}
          previewUrl={previewUrl || ""}
          isVideo={isVideo}
          onPreview={onOpen}
          onClear={handleClear}
        />
      )}

      {/* 预览模态框 */}
      <PreviewModal
        isOpen={isOpen}
        onClose={onClose}
        previewUrl={previewUrl}
        isVideo={isVideo}
      />
    </div>
  )
}
