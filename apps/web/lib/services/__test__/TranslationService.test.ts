import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { z } from "zod";
import {
  TranslationError,
  translateText,
  translateObject,
  translateTextToMultipleLanguages,
  createTranslationService,
  DEFAULT_PROMPTS,
  TranslationServiceConfig
} from "../../../../../packages/utils/src/server/TranslationService";
import { chatModel, MODEL_DOUBAO_PRO_32K } from "@repo/utils/server";
import * as ai from "ai";

// 模拟 ai 模块
vi.mock("ai", async () => {
  const actual = await vi.importActual("ai");
  return {
    ...actual as any,
    generateObject: vi.fn(),
  };
});

// 模拟 chatModel 函数
vi.mock("@repo/utils/server", async () => {
  const actual = await vi.importActual("@repo/utils/server");
  return {
    ...actual as any,
    chatModel: vi.fn(() => "mockedModel"),
  };
});

describe("TranslationService", () => {
  let translationConfig: TranslationServiceConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    translationConfig = createTranslationService("mockedModel" as any, 5000);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("createTranslationService", () => {
    it("应该创建翻译服务配置", () => {
      const config = createTranslationService("testModel" as any, 10000);
      expect(config).toEqual({
        model: "testModel",
        timeout: 10000
      });
    });

    it("应该使用默认超时时间", () => {
      const config = createTranslationService("testModel" as any);
      expect(config).toEqual({
        model: "testModel",
        timeout: 30000
      });
    });
  });

  describe("DEFAULT_PROMPTS", () => {
    it("应该生成文本翻译提示词", () => {
      const prompt = DEFAULT_PROMPTS.text(
        "你好世界",
        "zh-CN",
        "en-US",
        true
      );

      expect(prompt).toContain("Translate the following zh-CN text into en-US");
      expect(prompt).toContain("你好世界");
      expect(prompt).toContain("game localization project");
      expect(prompt).toContain("Preserve all formatting");
    });

    it("应该生成对象翻译提示词", () => {
      const testObject = { title: "测试", description: "描述" };
      const prompt = DEFAULT_PROMPTS.object(
        testObject,
        "zh-CN",
        "en-US",
        false,
        ["title"]
      );

      expect(prompt).toContain("Translate the following JSON object from zh-CN to en-US");
      expect(prompt).toContain(JSON.stringify(testObject, null, 2));
      expect(prompt).toContain("Only translate values, NOT keys");
      expect(prompt).toContain("Only translate the following fields: title");
    });
  });

  describe("translateText", () => {
    it("应该成功翻译文本", async () => {
      // 模拟 generateObject 的成功响应
      (ai.generateObject as any).mockResolvedValue({
        object: {
          translatedText: "Hello World",
          detectedLanguage: "zh-CN",
        },
      });

      const result = await translateText(
        "你好世界",
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
        },
        translationConfig
      );

      expect(result.translatedText).toBe("Hello World");
      expect(result.detectedLanguage).toBe("zh-CN");
      expect(ai.generateObject).toHaveBeenCalledTimes(1);

      // 验证传递给 generateObject 的参数
      const callArgs = (ai.generateObject as any).mock.calls[0][0];
      expect(callArgs.model).toBe("mockedModel");
      expect(callArgs.prompt).toContain("你好世界");
      expect(callArgs.prompt).toContain("game localization project");
    });

    it("应该使用自定义提示词", async () => {
      (ai.generateObject as any).mockResolvedValue({
        object: {
          translatedText: "Game Controls: Tap to Jump",
        },
      });

      const customPrompt = "Custom prompt for game instructions";
      await translateText(
        "游戏操作说明",
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
          customPrompt
        },
        translationConfig
      );

      // 验证使用了自定义提示词
      const callArgs = (ai.generateObject as any).mock.calls[0][0];
      expect(callArgs.prompt).toBe(customPrompt);
      expect(callArgs.prompt).not.toContain("game localization project");
    });

    it("应该处理空文本", async () => {
      const result = await translateText(
        "",
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
        },
        translationConfig
      );

      expect(result.translatedText).toBe("");
      expect(ai.generateObject).not.toHaveBeenCalled();
    });

    it("应该处理翻译错误", async () => {
      // 模拟 generateObject 抛出错误
      (ai.generateObject as any).mockRejectedValue(
        new Error("Failed to generate object")
      );

      await expect(
        translateText(
          "你好世界",
          {
            sourceLanguage: "zh-CN",
            targetLanguage: "en-US",
          },
          translationConfig
        )
      ).rejects.toThrow(TranslationError);
    });

    it("应该处理超时错误", async () => {
      // 模拟超时错误
      (ai.generateObject as any).mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => {
            const error = new Error("Aborted");
            error.name = "AbortError";
            reject(error);
          }, 10);
        });
      });

      await expect(
        translateText(
          "你好世界",
          {
            sourceLanguage: "zh-CN",
            targetLanguage: "en-US",
            timeout: 5,
          },
          translationConfig
        )
      ).rejects.toThrow(/Translation timeout/);
    });
  });

  describe("translateObject", () => {
    const testSchema = z.object({
      title: z.string(),
      description: z.string(),
      tags: z.array(z.string()),
    });

    const testObject = {
      title: "测试标题",
      description: "这是一个测试描述",
      tags: ["测试", "示例"],
    };

    it("应该成功翻译对象", async () => {
      // 模拟 generateObject 的成功响应
      (ai.generateObject as any).mockResolvedValue({
        object: {
          title: "Test Title",
          description: "This is a test description",
          tags: ["Test", "Example"],
        },
      });

      const result = await translateObject(
        testObject,
        testSchema,
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
        },
        translationConfig
      );

      expect(result.title).toBe("Test Title");
      expect(result.description).toBe("This is a test description");
      expect(result.tags).toEqual(["Test", "Example"]);
      expect(ai.generateObject).toHaveBeenCalledTimes(1);

      // 验证传递给 generateObject 的参数
      const callArgs = (ai.generateObject as any).mock.calls[0][0];
      expect(callArgs.model).toBe("mockedModel");
      expect(callArgs.prompt).toContain(JSON.stringify(testObject, null, 2));
      expect(callArgs.prompt).toContain("game localization project");
    });

    it("应该使用自定义对象提示词", async () => {
      (ai.generateObject as any).mockResolvedValue({
        object: {
          title: "Test Title",
          description: "This is a test description",
          tags: ["Test", "Example"],
        },
      });

      const customPrompt = "Custom prompt for game object translation";
      await translateObject(
        testObject,
        testSchema,
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
          customObjectPrompt: customPrompt
        },
        translationConfig
      );

      // 验证使用了自定义提示词
      const callArgs = (ai.generateObject as any).mock.calls[0][0];
      expect(callArgs.prompt).toBe(customPrompt);
    });

    it("应该处理空对象", async () => {
      const result = await translateObject(
        {} as any,
        testSchema,
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
        },
        translationConfig
      );

      expect(result).toEqual({});
      expect(ai.generateObject).not.toHaveBeenCalled();
    });

    it("应该处理对象翻译错误", async () => {
      // 模拟 generateObject 抛出错误
      (ai.generateObject as any).mockRejectedValue(
        new Error("Failed to generate object")
      );

      await expect(
        translateObject(
          testObject,
          testSchema,
          {
            sourceLanguage: "zh-CN",
            targetLanguage: "en-US",
          },
          translationConfig
        )
      ).rejects.toThrow(TranslationError);
    });

    it("应该只翻译指定字段", async () => {
      (ai.generateObject as any).mockImplementation((_options: any) => {
        // 检查提示词中是否包含指定字段
        return {
          object: {
            title: "Test Title",
            description: "这是一个测试描述", // 未翻译
            tags: ["Test", "Example"],
          },
          finishReason: "stop",
          usage: { promptTokens: 10, completionTokens: 10, totalTokens: 20 },
          warnings: [],
          request: {}
        };
      });

      await translateObject(
        testObject,
        testSchema,
        {
          sourceLanguage: "zh-CN",
          targetLanguage: "en-US",
          fieldsToTranslate: ["title", "tags"]
        },
        translationConfig
      );

      // 验证提示词中包含指定字段
      const callArgs = (ai.generateObject as any).mock.calls[0][0];
      expect(callArgs.prompt).toContain("Only translate the following fields: title, tags");
    });
  });

  describe("translateTextToMultipleLanguages", () => {
    it("应该并行翻译文本到多种语言", async () => {
      // 模拟 translateText 函数
      vi.spyOn(ai, "generateObject").mockImplementation((options: any) => {
        const prompt = options.prompt as string;
        const targetLanguage = prompt.includes("en-US") ? "en-US" :
                              prompt.includes("fr-FR") ? "fr-FR" : "es-ES";

        const translations = {
          "en-US": "Hello World",
          "fr-FR": "Bonjour le monde",
          "es-ES": "Hola Mundo"
        };

        return Promise.resolve({
          object: {
            translatedText: translations[targetLanguage],
            detectedLanguage: "zh-CN"
          },
          finishReason: "stop",
          usage: { promptTokens: 10, completionTokens: 10, totalTokens: 20 },
          warnings: [],
          request: {}
        } as any);
      });

      const result = await translateTextToMultipleLanguages(
        "你好世界",
        {
          sourceLanguage: "zh-CN",
          targetLanguages: ["en-US", "fr-FR", "es-ES"],
        },
        translationConfig
      );

      expect(result["en-US"]?.translatedText).toBe("Hello World");
      expect(result["fr-FR"]?.translatedText).toBe("Bonjour le monde");
      expect(result["es-ES"]?.translatedText).toBe("Hola Mundo");
      expect(ai.generateObject).toHaveBeenCalledTimes(3);
    });

    it("应该处理空文本", async () => {
      const result = await translateTextToMultipleLanguages(
        "",
        {
          sourceLanguage: "zh-CN",
          targetLanguages: ["en-US", "fr-FR"],
        },
        translationConfig
      );

      expect(result["en-US"]?.translatedText).toBe("");
      expect(result["fr-FR"]?.translatedText).toBe("");
      expect(ai.generateObject).not.toHaveBeenCalled();
    });

    it("应该处理部分语言翻译失败的情况", async () => {
      // 模拟第一个成功，第二个失败
      vi.spyOn(ai, "generateObject").mockImplementation((options: any) => {
        const prompt = options.prompt as string;
        if (prompt.includes("en-US")) {
          return Promise.resolve({
            object: {
              translatedText: "Hello World",
              detectedLanguage: "zh-CN"
            },
            finishReason: "stop",
            usage: { promptTokens: 10, completionTokens: 10, totalTokens: 20 },
            warnings: [],
            request: {}
          } as any);
        } else {
          return Promise.reject(new Error("Translation failed"));
        }
      });

      const result = await translateTextToMultipleLanguages(
        "你好世界",
        {
          sourceLanguage: "zh-CN",
          targetLanguages: ["en-US", "fr-FR"],
        },
        translationConfig
      );

      expect(result["en-US"]?.translatedText).toBe("Hello World");
      expect(result["fr-FR"]?.translatedText).toBe("你好世界"); // 失败时返回原文
      // 检查是否包含错误信息
      const frResult = result["fr-FR"] as any;
      expect(frResult.error).toBeDefined();
    });
  });
});
