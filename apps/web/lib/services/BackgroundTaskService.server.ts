import { prisma } from "@repo/db"
import { postMessages } from "./MQApiService"
import { OT_TRANSLATION_TASK } from "@lib/consts"
import { BACKGROUND_TASK_STATUS, BACKGROUND_TASK_TYPE, type BackgroundTaskStatus, type BackgroundTaskType } from "@repo/shared-types"

export interface CreateBackgroundTaskParams {
  type: BackgroundTaskType
  projectId: string
  userId: string
  title: string
  description?: string
  parameters: Record<string, any>
  maxRetries?: number
}

export interface TranslationTaskParams {
  // 翻译的内容类型 (metadata, content, etc.)
  contentType: string
  // 翻译的内容ID
  contentId: string
  // 源语言
  sourceLocale: string
  // 目标语言列表
  targetLocales: string[]
  // 需要翻译的字段
  fieldsToTranslate?: string[]
  // 额外参数
  extraParams?: Record<string, any>
}

/**
 * 创建后台任务并发送MQ消息
 */
export async function createBackgroundTask(params: CreateBackgroundTaskParams): Promise<string> {
  const {
    type,
    projectId,
    userId,
    title,
    description,
    parameters,
    maxRetries = 3
  } = params

  return await prisma.$transaction(async (tx) => {
    // 创建后台任务记录
    const task = await tx.backgroundTask.create({
      data: {
        type,
        projectId,
        userId,
        title,
        description,
        parameters,
        status: BACKGROUND_TASK_STATUS.PENDING,
        maxRetries,
        retryCount: 0
      }
    })

    // 发送MQ消息
    const messageData = {
      taskId: task.id,
      type,
      projectId,
      userId,
      parameters
    }

    // 根据任务类型选择不同的队列
    let queueName = `${process.env.PROJECT_QUEUE_NAME}_background_tasks`
    let operatorType = ""

    switch (type) {
      case BACKGROUND_TASK_TYPE.TRANSLATION:
        queueName = `${process.env.PROJECT_QUEUE_NAME}_translation_tasks`
        operatorType = OT_TRANSLATION_TASK
        break
      default:
        throw new Error(`Unsupported task type: ${type}`)
    }

    const messageId = await postMessages(operatorType, messageData, queueName)

    // 更新任务记录的消息ID
    await tx.backgroundTask.update({
      where: { id: task.id },
      data: { messageId }
    })

    return task.id
  })
}

/**
 * 创建翻译任务
 */
export async function createTranslationTask(
  projectId: string,
  userId: string,
  params: TranslationTaskParams
): Promise<string> {
  const { contentType, contentId, sourceLocale, targetLocales } = params

  const title = `翻译任务: ${contentType} (${contentId})`
  const description = `将 ${sourceLocale} 翻译为 ${targetLocales.join(', ')}`

  return await createBackgroundTask({
    type: BACKGROUND_TASK_TYPE.TRANSLATION,
    projectId,
    userId,
    title,
    description,
    parameters: params
  })
}

/**
 * 更新任务状态
 */
export async function updateTaskStatus(
  taskId: string,
  status: BackgroundTaskStatus,
  result?: Record<string, any>,
  errorMessage?: string
): Promise<void> {
  const updateData: any = {
    status,
    updatedAt: new Date()
  }

  if (status === BACKGROUND_TASK_STATUS.PROCESSING) {
    updateData.startedAt = new Date()
  }

  if (status === BACKGROUND_TASK_STATUS.SUCCESS || status === BACKGROUND_TASK_STATUS.FAILED) {
    updateData.completedAt = new Date()
  }

  if (result) {
    updateData.result = result
  }

  if (errorMessage) {
    updateData.errorMessage = errorMessage
  }

  await prisma.backgroundTask.update({
    where: { id: taskId },
    data: updateData
  })
}

/**
 * 增加重试次数
 */
export async function incrementRetryCount(taskId: string): Promise<number> {
  const task = await prisma.backgroundTask.update({
    where: { id: taskId },
    data: {
      retryCount: {
        increment: 1
      }
    }
  })

  return task.retryCount
}

/**
 * 重新发送任务到队列
 */
export async function requeueTask(taskId: string): Promise<void> {
  const task = await prisma.backgroundTask.findUnique({
    where: { id: taskId }
  })

  if (!task) {
    throw new Error(`Task not found: ${taskId}`)
  }

  // 重置状态为待处理
  await updateTaskStatus(taskId, BACKGROUND_TASK_STATUS.PENDING)

  // 重新发送MQ消息
  const messageData = {
    taskId: task.id,
    type: task.type,
    projectId: task.projectId,
    userId: task.userId,
    parameters: task.parameters
  }

  let queueName = `${process.env.PROJECT_QUEUE_NAME}_background_tasks`
  let operatorType = ""

  switch (task.type) {
    case BACKGROUND_TASK_TYPE.TRANSLATION:
      queueName = `${process.env.PROJECT_QUEUE_NAME}_translation_tasks`
      operatorType = OT_TRANSLATION_TASK
      break
    default:
      throw new Error(`Unsupported task type: ${task.type}`)
  }

  const messageId = await postMessages(operatorType, messageData, queueName)

  // 更新消息ID
  await prisma.backgroundTask.update({
    where: { id: taskId },
    data: { messageId }
  })
}


