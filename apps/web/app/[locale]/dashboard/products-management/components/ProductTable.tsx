"use client"

import { useEffect, useState } from "react"
import {
  DataTable,
  DataTableContent,
  DataTablePagination,
  DataTableToolbar,
  DataTableFilter,
  DataTableFilterField,
  DataTableFilterActions,
  Input,
  Button
} from "@repo/ui/components"
import { useTable } from "@repo/ui/hooks"
import { Edit, Trash } from "lucide-react"
import { Product } from "@repo/db"
import { fetchGet, fetchDelete } from "@repo/utils/react"
import { toast } from "sonner"
import { useConfirm } from "@repo/ui/hooks"
import { formatCurrency } from "@repo/utils"

interface ProductTableProps {
  onEdit: (product: Product) => void
  onCreate: () => void
}

export default function ProductTable({ onEdit, onCreate }: ProductTableProps) {
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [totalItems, setTotalItems] = useState(0)
  const [filters, setFilters] = useState({
    code: "",
    name: ""
  })
  const { ConfirmDialog, confirm } = useConfirm()

  // 定义表格列
  const columns = [
    {
      id: "index",
      header: "序号",
      cell: ({ row }: { row: any }) => row.index + 1,
    },
    {
      accessorKey: "code",
      header: "产品代码",
    },
    {
      accessorKey: "name",
      header: "产品名称",
    },
    {
      accessorKey: "type",
      header: "产品类型",
    },
    {
      accessorKey: "price",
      header: "原价",
      cell: ({ row }: { row: any }) => {
        const price = row.getValue("price") as number
        return formatCurrency(price, { unit: "f" })
      }
    },
    {
      accessorKey: "discountPrice",
      header: "折扣价",
      cell: ({ row }: { row: any }) => {
        const discountPrice = row.getValue("discountPrice") as number
        return formatCurrency(discountPrice, { unit: "f" })
      }
    },
    {
      accessorKey: "discount",
      header: "折扣",
      cell: ({ row }: { row: any }) => {
        const discount = row.getValue("discount") as number
        return `${(discount * 100).toFixed(0)}%`
      }
    },
    {
      accessorKey: "status",
      header: "状态",
      cell: ({ row }: { row: any }) => {
        const status = row.getValue("status") as string
        return status === "ACTIVE" ?
          <span className="text-green-600">激活</span> :
          <span className="text-red-600">禁用</span>
      }
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }: { row: any }) => (
        <div className="flex gap-2">
          <button
            type="button"
            className="text-blue-600 hover:text-blue-800"
            onClick={() => onEdit(row.original as Product)}
          >
            <Edit size={16} />
          </button>
          <button
            type="button"
            className="text-red-600 hover:text-red-800"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash size={16} />
          </button>
        </div>
      ),
    },
  ]

  // 加载产品数据
  const loadProducts = async (page = 1) => {
    setIsLoading(true)
    try {
      // 构建查询参数
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pagination.pageSize.toString()
      })

      // 添加过滤条件
      if (filters.code) {
        params.append('code', filters.code)
      }
      if (filters.name) {
        params.append('name', filters.name)
      }

      const response = await fetchGet<{
        data: Product[]
        total: number
      }>(`/api/products-management?${params.toString()}`)

      if (response) {
        setProducts(response.data)
        setTotalItems(response.total)
      }
    } catch (error) {
      console.error("加载产品失败:", error)
      toast.error("加载产品失败")
    } finally {
      setIsLoading(false)
    }
  }

  // 处理过滤器提交
  const handleFilterSubmit = (values: any) => {
    setFilters({
      code: values.code || "",
      name: values.name || ""
    })
    loadProducts(1)
  }

  // 删除产品
  const handleDelete = async (id: number) => {
    const confirmed = await confirm({
      title: "确认删除",
      description: "确定要删除这个产品吗？此操作不可撤销。",
      confirmText: "删除",
      cancelText: "取消",
    })

    if (confirmed) {
      try {
        await fetchDelete(`/api/products-management/delete?id=${id}`)
        toast.success("产品删除成功")
        loadProducts(pagination.page)
      } catch (error) {
        console.error("删除产品失败:", error)
        toast.error("删除产品失败")
      }
    }
  }

  // 使用表格钩子
  const {
    table,
    contentProps,
    paginationProps,
    toolbarProps,
    pagination,
  } = useTable({
    columns,
    data: products,
    manualPagination: true,
    totalItems,
    initialPage: 1,
    initialPageSize: 10,
    isLoading,
    onPageChange: (page) => {
      loadProducts(page)
    },
    onRefresh: () => {
      loadProducts(1)
    },
    onCreate,
    onCreateLabel: "创建产品",
  })

  // 初始加载和页码变化时重新加载数据
  useEffect(() => {
    loadProducts(pagination.page)
  }, [pagination.page])

  return (
    <>
      {ConfirmDialog}
      <DataTable table={table} isLoading={isLoading}>
        <DataTableFilter
          table={table}
          onSubmit={handleFilterSubmit}
          onReset={() => {
            setFilters({ code: "", name: "" })
            loadProducts(1)
          }}
        >
          <DataTableFilterField name="code">
            <Input placeholder="产品代码" />
          </DataTableFilterField>

          <DataTableFilterField name="name">
            <Input placeholder="产品名称" />
          </DataTableFilterField>

          <DataTableFilterActions>
            <Button type="submit">筛选</Button>
            <Button type="reset" variant="outline">重置</Button>
          </DataTableFilterActions>
        </DataTableFilter>

        <DataTableToolbar {...toolbarProps} />

        <DataTableContent {...contentProps} />

        <DataTablePagination {...paginationProps} />
      </DataTable>
    </>
  )
}
