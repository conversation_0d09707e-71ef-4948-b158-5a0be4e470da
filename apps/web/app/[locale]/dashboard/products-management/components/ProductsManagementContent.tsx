"use client"

import { useState, useEffect } from "react"
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@repo/ui/components"
import ProductTable from "./ProductTable"
import ProductForm from "./ProductForm"
import { Product, Benefit } from "@repo/db"

// 扩展 Product 类型，添加 benefitRules 属性
interface ProductWithBenefitRules extends Partial<Product> {
	benefitRules?: Array<{
		benefitCode: string
		maxQuantity?: number
		validDays?: number
	}>
}
import { fetchGet } from "@repo/utils/react"
import { toast } from "sonner"

export default function ProductsManagementContent() {
	const [selectedProduct, setSelectedProduct] =
		useState<ProductWithBenefitRules | null>(null)
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const [benefits, setBenefits] = useState<Benefit[]>([])
	const [isLoadingBenefits, setIsLoadingBenefits] = useState(false)

	// 加载权益列表
	useEffect(() => {
		const loadBenefits = async () => {
			setIsLoadingBenefits(true)
			try {
				const response = await fetchGet<{
					data: Benefit[]
				}>("/api/benefits-management?pageSize=100")

				if (response) {
					setBenefits(response.data)
				}
			} catch (error) {
				console.error("加载权益失败:", error)
				toast.error("加载权益失败")
			} finally {
				setIsLoadingBenefits(false)
			}
		}

		loadBenefits()
	}, [])

	// 处理选择产品进行编辑
	const handleEditProduct = async (product: Product) => {
		try {
			// 获取产品权益规则
			if (product.id) {
				const response = await fetchGet<{
					data: {
						benefitCode: string
						maxQuantity: number
						validDays: number
					}[]
				}>(`/api/products-management/detail?id=${product.id}`)

				if (response) {
					// 将产品和权益规则合并
					setSelectedProduct({
						...(response as any),
					})
				}
			} else {
				setSelectedProduct(product)
			}
			setIsDialogOpen(true)
		} catch (error) {
			console.error("加载产品权益规则失败:", error)
			toast.error("加载产品权益规则失败")
			// 即使加载权益规则失败，也显示产品编辑表单
			setSelectedProduct(product)
			setIsDialogOpen(true)
		}
	}

	// 处理创建新产品
	const handleCreateProduct = () => {
		setSelectedProduct({})
		setIsDialogOpen(true)
	}

	// 处理取消编辑
	const handleCancelEdit = () => {
		setIsDialogOpen(false)
		setSelectedProduct(null)
	}

	// 处理保存成功
	const handleSaveSuccess = () => {
		setIsDialogOpen(false)
		setSelectedProduct(null)
	}

	return (
		<div className="container mx-auto p-6">
			<Card>
				<CardHeader>
					<CardTitle>产品列表</CardTitle>
				</CardHeader>
				<CardContent>
					<ProductTable
						onEdit={handleEditProduct}
						onCreate={handleCreateProduct}
					/>
				</CardContent>
			</Card>

			{/* 产品表单对话框 */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>
							{selectedProduct?.id ? "编辑产品" : "创建产品"}
						</DialogTitle>
					</DialogHeader>

					{selectedProduct !== null && (
						<ProductForm
							product={selectedProduct}
							benefits={benefits}
							isLoadingBenefits={isLoadingBenefits}
							onCancel={handleCancelEdit}
							onSuccess={handleSaveSuccess}
						/>
					)}
				</DialogContent>
			</Dialog>
		</div>
	)
}
