"use client"

import { useState } from "react"
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Textarea,
	Select,
	SelectTrigger,
	SelectValue,
	SelectContent,
	SelectItem,
	Button,
	Checkbox,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	Badge,
	Popover,
	PopoverContent,
	PopoverTrigger,
	Switch,
	FormDescription,
} from "@repo/ui/components"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Product, Benefit } from "@repo/db"
import { fetchPost, fetchPut } from "@repo/utils/react"
import { safeJsonParse } from "@repo/utils"
import {
	fenToYuan,
	yuanToFen,
	decimalToPercent,
	percentToDecimal,
} from "@repo/utils"

// 扩展 Product 类型，添加 benefitRules 属性
interface ProductWithBenefitRules extends Partial<Product> {
	benefitRules?: Array<{
		benefitCode: string
		maxQuantity?: number
		validDays?: number
	}>
}
import { toast } from "sonner"
import { ProductStatus } from "@repo/shared-types"

// 表单验证模式
const productFormSchema = z.object({
	code: z.string().min(1, "产品代码不能为空"),
	name: z.string().min(1, "产品名称不能为空"),
	description: z.string().min(1, "产品描述不能为空"),
	type: z.string().min(1, "产品类型不能为空"),
	price: z.coerce.number().int().min(0, "价格不能为负数"),
	currency: z.string().min(1, "币种不能为空"),
	credits: z.coerce.number().int().min(0, "信用点不能为负数"),
	siteCount: z.coerce.number().int().min(0, "站点数量不能为负数"),
	discount: z.coerce.number().min(0).max(1, "折扣必须在0-1之间"),
	discountPrice: z.coerce.number().int().min(0, "折扣价不能为负数"),
	isPopular: z.boolean().optional().default(false),
	sortOrder: z.coerce.number().int().default(0),
	status: z.string().min(1, "状态不能为空"),
	locale: z.string().min(1, "语言不能为空"),
	benefitRules: z
		.array(
			z.object({
				benefitCode: z.string(),
				benefitName: z.string(),
				maxQuantity: z.coerce.number().int(),
				validDays: z.coerce.number().int(),
			}),
		)
		.optional(),
	features: z.any().optional(),
	metadata: z.any().optional(),
})

type ProductFormValues = z.infer<typeof productFormSchema>

interface ProductFormProps {
	product: ProductWithBenefitRules
	benefits: Benefit[]
	isLoadingBenefits?: boolean
	onCancel: () => void
	onSuccess: () => void
}

export default function ProductForm({
	product,
	benefits,
	isLoadingBenefits = false,
	onCancel,
	onSuccess,
}: ProductFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false)
	const isEditing = !!product.id

	// isLoadingBenefits属性已在组件参数中定义

	// 初始化表单
	const form = useForm<ProductFormValues>({
		resolver: zodResolver(productFormSchema) as any,
		defaultValues: {
			code: product.code || "",
			name: product.name || "",
			description: product.description || "",
			type: product.type || "membership",
			price: product.price || 0,
			currency: product.currency || "CNY",
			credits: product.credits || 0,
			siteCount: product.siteCount || 1,
			discount: product.discount || 1.0,
			discountPrice: product.discountPrice || 0,
			isPopular: product.isPopular || false,
			sortOrder: product.sortOrder || 0,
			status: product.status || ProductStatus.ACTIVE,
			locale: product.locale || "zh-CN",
			features: product.features || [],
			metadata: product.metadata || {},
			benefitRules: product.benefitRules || [],
		},
	})

	// 搜索权益
	const [searchTerm, setSearchTerm] = useState("")
	const [commandOpen, setCommandOpen] = useState(false)

	// 过滤权益列表
	const filteredBenefits = benefits.filter(
		(benefit) =>
			benefit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			benefit.code.toLowerCase().includes(searchTerm.toLowerCase()),
	)

	// 提交表单
	const onSubmit = async (values: ProductFormValues) => {
		setIsSubmitting(true)

		try {
			// 计算折扣价 - 价格已经是分，折扣已经是小数，直接计算即可
			if (values.price && values.discount) {
				values.discountPrice = Math.round(values.price * values.discount)
			}

			// 处理features和metadata
			values.features =
				values.benefitRules?.map((rule) => rule.benefitName) ?? []
			values.metadata = safeJsonParse(values.metadata, {})

			if (isEditing) {
				// 更新现有产品
				await fetchPut(
					`/api/products-management/update?id=${product.id}`,
					values,
				)
				toast.success("产品更新成功")
			} else {
				// 创建新产品
				await fetchPost("/api/products-management", values)
				toast.success("产品创建成功")
			}

			onSuccess()
		} catch (error) {
			console.error("保存产品失败:", error)
			toast.error("保存产品失败")
		} finally {
			setIsSubmitting(false)
		}
	}

	// 计算折扣价
	const calculateDiscountPrice = () => {
		const price = form.getValues("price")
		const discount = form.getValues("discount")

		if (price && discount) {
			const discountPrice = Math.round(price * discount)
			form.setValue("discountPrice", discountPrice)
		}
	}

	// 不再需要初始化产品权益规则的useEffect，因为我们在defaultValues中已经设置了benefitRules

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				<FormField
					control={form.control}
					name="code"
					render={({ field }) => (
						<FormItem>
							<FormLabel>产品代码</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="输入产品代码"
									disabled={isEditing} // 编辑模式下不允许修改代码
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>产品名称</FormLabel>
								<FormControl>
									<Input {...field} placeholder="输入产品名称" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="type"
						render={({ field }) => (
							<FormItem>
								<FormLabel>产品类型</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="选择产品类型" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="membership">会员</SelectItem>
										<SelectItem value="credits">星钻</SelectItem>
										<SelectItem value="trend-words">趋势词</SelectItem>
										<SelectItem value="default">默认用户</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={form.control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormLabel>描述</FormLabel>
							<FormControl>
								<Textarea {...field} placeholder="输入产品描述" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="price"
						render={({ field }) => {
							// 将分转换为元显示
							const yuanValue = field.value ? fenToYuan(field.value, true) : 0

							return (
								<FormItem>
									<FormLabel>原价(元)</FormLabel>
									<FormControl>
										<Input
											value={yuanValue}
											type="number"
											min={0}
											step={0.01}
											placeholder="输入原价"
											onChange={(e) => {
												// 将元转换为分存储
												const fenValue = yuanToFen(e.target.value)
												field.onChange(fenValue)
												calculateDiscountPrice()
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)
						}}
					/>

					<FormField
						control={form.control}
						name="currency"
						render={({ field }) => (
							<FormItem>
								<FormLabel>币种</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="选择币种" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="CNY">人民币</SelectItem>
										<SelectItem value="USD">美元</SelectItem>
										<SelectItem value="EUR">欧元</SelectItem>
										<SelectItem value="HKD">港币</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="discount"
						render={({ field }) => {
							// 将小数转换为百分比显示
							const percentValue = field.value
								? decimalToPercent(field.value, { withSymbol: false })
								: "100"

							return (
								<FormItem>
									<FormLabel>折扣(%)</FormLabel>
									<FormControl>
										<Input
											value={percentValue}
											type="number"
											min={0}
											max={100}
											step={1}
											placeholder="输入折扣"
											onChange={(e) => {
												// 将百分比转换为小数存储
												const decimalValue = percentToDecimal(e.target.value)
												field.onChange(decimalValue)
												calculateDiscountPrice()
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)
						}}
					/>

					<FormField
						control={form.control}
						name="discountPrice"
						render={({ field }) => {
							// 将分转换为元显示
							const yuanValue = field.value ? fenToYuan(field.value, true) : 0

							return (
								<FormItem>
									<FormLabel>折扣价(元)</FormLabel>
									<FormControl>
										<Input
											value={yuanValue}
											type="number"
											min={0}
											step={0.01}
											placeholder="输入折扣价"
											onChange={(e) => {
												// 将元转换为分存储
												const fenValue = yuanToFen(e.target.value)
												field.onChange(fenValue)

												// 如果原价存在，根据折扣价反向计算折扣
												const price = form.getValues("price")
												if (price && fenValue) {
													const newDiscount = fenValue / price
													form.setValue("discount", newDiscount)
												}
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)
						}}
					/>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="credits"
						render={({ field }) => (
							<FormItem>
								<FormLabel>星钻数</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="number"
										min={0}
										placeholder="输入可获取星钻数量"
									/>
								</FormControl>
								<FormDescription className="text-xs">
									购买后可获得星钻数量
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="siteCount"
						render={({ field }) => (
							<FormItem>
								<FormLabel>游戏数量</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="number"
										min={0}
										placeholder="输入游戏数量"
									/>
								</FormControl>
								<FormDescription className="text-xs">
									购买后可添加游戏数量，0表示不限制（仅用于显示）
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="locale"
						render={({ field }) => (
							<FormItem>
								<FormLabel>语言</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
									disabled
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="选择语言" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="en">英文</SelectItem>
										<SelectItem value="zh-CN">简体中文</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="status"
						render={({ field }) => (
							<FormItem>
								<FormLabel>状态</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="选择状态" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value={ProductStatus.ACTIVE}>激活</SelectItem>
										<SelectItem value={ProductStatus.DISABLED}>禁用</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={form.control}
						name="sortOrder"
						render={({ field }) => (
							<FormItem>
								<FormLabel>排序顺序</FormLabel>
								<FormControl>
									<Input {...field} type="number" placeholder="输入排序顺序" />
								</FormControl>
								<FormDescription className="text-xs">
									排序顺序，数字越大越靠前
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="isPopular"
						render={({ field }) => (
							<FormItem>
								<FormLabel>是否热门产品</FormLabel>
								<div className="py-2 flex items-center gap-2">
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
									<span className="text-sm text-muted-foreground">
										{field.value ? "热门产品" : "普通产品"}
									</span>
								</div>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* 权益选择 */}
				<FormField
					control={form.control}
					name="benefitRules"
					render={({ field }) => {
						// 从benefitRules中提取已选择的权益代码
						const selectedBenefitCodes =
							field.value?.map((rule) => rule.benefitCode) || []

						// 处理权益选择变化
						const handleBenefitChange = (
							benefitCode: string,
							checked: boolean,
							benefit: Benefit,
						) => {
							const currentRules = [...(field.value || [])]

							if (checked) {
								// 添加新权益
								if (!selectedBenefitCodes.includes(benefitCode)) {
									field.onChange([
										...currentRules,
										{
											benefitCode,
											benefitName: benefit.name,
											maxQuantity: 0, // 默认无限制
											validDays: 0, // 默认永久有效
										},
									])
								}
							} else {
								// 移除权益
								field.onChange(
									currentRules.filter(
										(rule) => rule.benefitCode !== benefitCode,
									),
								)
							}
						}

						// 更新权益规则
						const updateBenefitRule = (
							benefitCode: string,
							fieldName: "maxQuantity" | "validDays",
							value: number,
						) => {
							const currentRules = [...(field.value || [])]
							const updatedRules = currentRules.map((rule) => {
								if (rule.benefitCode === benefitCode) {
									return { ...rule, [fieldName]: value }
								}
								return rule
							})

							field.onChange(updatedRules)
						}

						return (
							<FormItem>
								<Card>
									<CardHeader>
										<CardTitle>关联权益</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-4">
											{/* 权益选择器 */}
											<Popover open={commandOpen} onOpenChange={setCommandOpen}>
												<PopoverTrigger asChild>
													<Button
														variant="outline"
														aria-expanded={commandOpen}
														className="w-full justify-between"
													>
														{selectedBenefitCodes.length > 0
															? `已选择 ${selectedBenefitCodes.length} 项权益`
															: "选择关联权益"}
														<span className="ml-2 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
															{selectedBenefitCodes.length}
														</span>
													</Button>
												</PopoverTrigger>
												<PopoverContent className="w-full p-0">
													<Command>
														<CommandInput
															placeholder="搜索权益..."
															value={searchTerm}
															onValueChange={setSearchTerm}
														/>
														<CommandList>
															<CommandEmpty>未找到匹配的权益</CommandEmpty>
															<CommandGroup>
																{isLoadingBenefits ? (
																	<div className="flex items-center justify-center py-4">
																		<span className="text-sm text-muted-foreground">
																			加载权益中...
																		</span>
																	</div>
																) : (
																	filteredBenefits.map((benefit) => (
																		<CommandItem
																			key={benefit.code}
																			value={benefit.code}
																			onSelect={() => {
																				const isSelected =
																					selectedBenefitCodes.includes(
																						benefit.code,
																					)
																				handleBenefitChange(
																					benefit.code,
																					!isSelected,
																					benefit,
																				)
																			}}
																		>
																			<Checkbox
																				checked={selectedBenefitCodes.includes(
																					benefit.code,
																				)}
																				className="mr-2 h-4 w-4"
																			/>
																			<span>{benefit.name}</span>
																			<span className="ml-2 text-xs text-muted-foreground">
																				({benefit.code})
																			</span>
																		</CommandItem>
																	))
																)}
															</CommandGroup>
														</CommandList>
													</Command>
												</PopoverContent>
											</Popover>

											{/* 已选权益列表 */}
											<div className="space-y-2">
												<h3 className="text-sm font-medium">已选权益</h3>
												{selectedBenefitCodes.length === 0 ? (
													<p className="text-sm text-muted-foreground">
														尚未选择任何权益
													</p>
												) : (
													<div className="grid grid-cols-1 gap-3">
														{benefits
															.filter((benefit) =>
																selectedBenefitCodes.includes(benefit.code),
															)
															.map((benefit) => {
																// 查找当前权益的规则
																const rule = field.value?.find(
																	(r) => r.benefitCode === benefit.code,
																)

																return (
																	<div
																		key={benefit.code}
																		className="border p-3 rounded-md"
																	>
																		<div className="flex items-center justify-between">
																			<div className="flex items-center space-x-2">
																				<Badge
																					variant="outline"
																					className="font-normal"
																				>
																					{benefit.code}
																				</Badge>
																				<span className="text-sm font-medium">
																					{benefit.name}
																				</span>
																			</div>
																			<Button
																				variant="ghost"
																				size="sm"
																				onClick={() =>
																					handleBenefitChange(
																						benefit.code,
																						false,
																						benefit,
																					)
																				}
																			>
																				移除
																			</Button>
																		</div>

																		<div className="mt-3 grid grid-cols-2 gap-3">
																			<div>
																				<FormItem className="space-y-1">
																					<FormLabel
																						htmlFor={`maxQuantity-${benefit.code}`}
																						className="text-xs"
																					>
																						最大数量 (0表示无限制)
																					</FormLabel>
																					<FormControl>
																						<Input
																							id={`maxQuantity-${benefit.code}`}
																							type="number"
																							value={rule?.maxQuantity ?? -1}
																							onChange={(e) =>
																								updateBenefitRule(
																									benefit.code,
																									"maxQuantity",
																									parseInt(e.target.value),
																								)
																							}
																						/>
																					</FormControl>
																					<FormMessage />
																					<FormDescription className="text-xs">
																						限制可用数量
																					</FormDescription>
																				</FormItem>
																			</div>
																			<div>
																				<FormItem className="space-y-1">
																					<FormLabel
																						htmlFor={`validDays-${benefit.code}`}
																						className="text-xs"
																					>
																						有效天数 (0表示永久有效)
																					</FormLabel>
																					<FormControl>
																						<Input
																							id={`validDays-${benefit.code}`}
																							type="number"
																							value={rule?.validDays ?? -1}
																							onChange={(e) =>
																								updateBenefitRule(
																									benefit.code,
																									"validDays",
																									parseInt(e.target.value),
																								)
																							}
																						/>
																					</FormControl>
																					<FormMessage />
																					<FormDescription className="text-xs">
																						购买后权益有效天数，一个月=30天
																					</FormDescription>
																				</FormItem>
																			</div>
																		</div>
																	</div>
																)
															})}
													</div>
												)}
											</div>
										</div>
									</CardContent>
								</Card>
								<FormMessage />
							</FormItem>
						)
					}}
				/>

				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
						disabled={isSubmitting}
					>
						取消
					</Button>
					<Button type="submit" disabled={isSubmitting}>
						{isSubmitting ? "保存中..." : isEditing ? "更新" : "创建"}
					</Button>
				</div>
			</form>
		</Form>
	)
}
