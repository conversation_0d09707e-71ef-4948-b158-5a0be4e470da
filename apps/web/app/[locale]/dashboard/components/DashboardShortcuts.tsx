"use client"

import {
  <PERSON>,
  CardDescription,
  Card<PERSON>ooter,
  <PERSON><PERSON><PERSON>er,
  CardTitle
} from "@repo/ui/components"
import {
  BarChart,
  LineChart,
  Search,
  TrendingUp,
  AreaChart,
  Globe,
  Eye,
  PieChart,
  Activity,
  ExternalLink
} from "lucide-react"
import { <PERSON> } from "@repo/i18n"
import { useTranslations } from "next-intl"

/**
 * 快捷链接类型定义
 */
interface ShortcutLink {
  title: string
  description: string
  icon: React.ReactNode
  href: string
  color: string
  isExternal?: boolean
}

/**
 * DashboardShortcuts组件
 *
 * 在仪表盘页面中显示网站主常用的外部网址链接卡片
 * 提供SEO和分析工具的快速访问
 */
export default function DashboardShortcuts() {
  const t = useTranslations("Dashboard")
  
  // 定义快捷链接数据 - 网站主常用的外部工具链接
  const shortcuts: ShortcutLink[] = [
    {
      title: "Google 趋势",
      description: "查看搜索词的流行趋势和相关数据",
      icon: <TrendingUp className="h-5 w-5" />,
      href: "https://trends.google.com/",
      color: "text-blue-500",
      isExternal: true
    },
    {
      title: "Google 搜索控制台",
      description: "监控和维护网站在Google搜索结果中的表现",
      icon: <Search className="h-5 w-5" />,
      href: "https://search.google.com/search-console",
      color: "text-red-500",
      isExternal: true
    },
    {
      title: "Google 分析",
      description: "全面了解网站流量和用户行为数据",
      icon: <BarChart className="h-5 w-5" />,
      href: "https://analytics.google.com/",
      color: "text-yellow-500",
      isExternal: true
    },
    {
      title: "Bing 网站管理员",
      description: "优化网站在Bing搜索引擎中的表现",
      icon: <Globe className="h-5 w-5" />,
      href: "https://www.bing.com/webmasters/",
      color: "text-blue-400",
      isExternal: true
    },
    {
      title: "Microsoft Clarity",
      description: "用户行为分析和热图工具",
      icon: <Eye className="h-5 w-5" />,
      href: "https://clarity.microsoft.com/",
      color: "text-indigo-500",
      isExternal: true
    },
    {
      title: "Plausible Analytics",
      description: "简单、轻量级、注重隐私的网站分析",
      icon: <PieChart className="h-5 w-5" />,
      href: "https://appview.fafafa.ai/sites",
      color: "text-green-500",
      isExternal: true
    },
    {
      title: "Ahrefs",
      description: "强大的SEO工具和竞争对手分析平台",
      icon: <LineChart className="h-5 w-5" />,
      href: "https://ahrefs.com/",
      color: "text-orange-500",
      isExternal: true
    },
    {
      title: "网站性能分析",
      description: "检查和优化网站加载速度和性能",
      icon: <Activity className="h-5 w-5" />,
      href: "https://pagespeed.web.dev/",
      color: "text-purple-500",
      isExternal: true
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {shortcuts.map((shortcut, index) => (
        <Link 
          key={index} 
          href={shortcut.href}
          target={shortcut.isExternal ? "_blank" : undefined}
          rel={shortcut.isExternal ? "noopener noreferrer" : undefined}
          className="block h-full transition-transform hover:scale-[1.02]"
        >
          <Card className="h-full border hover:border-primary/50 hover:shadow-md transition-all duration-200">
            <CardHeader>
              <div className={`${shortcut.color} mb-2`}>
                {shortcut.icon}
              </div>
              <CardTitle className="flex items-center gap-1">
                {shortcut.title}
                {shortcut.isExternal && <ExternalLink className="h-3.5 w-3.5" />}
              </CardTitle>
              <CardDescription>{shortcut.description}</CardDescription>
            </CardHeader>
            <CardFooter className="text-sm text-muted-foreground">
              <div className="flex items-center">
                <span>{shortcut.isExternal ? "点击访问外部链接" : t("shortcuts.clickToAccess")}</span>
              </div>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </div>
  )
}
