"use client"

import { Avatar, AvatarFallback, Uploader } from "@repo/ui/components"
import { useTranslations } from "next-intl"

interface UserAvatarUploaderProps {
  value?: string
  onChange: (value: string) => void
  userName?: string
}

export function UserAvatarUploader({
  value,
  onChange,
  userName,
}: UserAvatarUploaderProps) {
  const t = useTranslations("Settings")

  // 获取用户名首字母作为头像占位符
  const getInitials = (name: string | undefined) => {
    if (!name) return "U"
    return name.charAt(0).toUpperCase()
  }

  return (
    <Uploader
      value={value}
      onFileChange={(files) => {
        // 如果是字符串（URL），直接使用
        if (typeof files[0] === "string") {
          onChange(files[0])
        }
        // 如果是文件对象，使用预览URL
        else if (files[0] && "preview" in files[0]) {
          onChange(files[0].preview)
        }
        // 如果没有文件，清空值
        else {
          onChange("")
        }
      }}
      accept="image/*"
      maxSize="2MB"
      variant="avatar"
      size="md"
      placeholder={
        <div className="flex flex-col items-center justify-center">
          <Avatar className="h-20 w-20">
            <AvatarFallback className="text-xl">
              {getInitials(userName)}
            </AvatarFallback>
          </Avatar>
          <span className="mt-2 text-xs text-muted-foreground">{t("avatar.placeholder")}</span>
        </div>
      }
    />
  )
}
