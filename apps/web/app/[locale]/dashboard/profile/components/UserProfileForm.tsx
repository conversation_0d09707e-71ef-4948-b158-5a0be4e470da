"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { User } from "@prisma/client"
import { useTranslations } from "next-intl"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "react-toastify"
import { z } from "zod"

import { refreshSession } from "@repo/auth/react"
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from "@repo/ui/components"
import { fetchPut } from "@repo/utils/react"
import { Edit, Loader2, Save, X } from "lucide-react"
import { UserAvatarUploader } from "./UserAvatarUploader"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, { message: "姓名必须至少包含2个字符" }),
  email: z
    .string()
    .min(1, { message: "邮箱地址不能为空" })
    .email({ message: "请输入有效的邮箱地址" })
    .regex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, {
      message: "邮箱格式不正确，请使用有效的邮箱格式",
    }),
  avatar: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface UserProfileFormProps {
  user: User
}

export function UserProfileForm({ user }: UserProfileFormProps) {
  const t = useTranslations("Settings")
  const [isEditing, setIsEditing] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: user.name || "",
      email: user.email || "",
      avatar: user.avatar || "",
    },
  })

  // 处理表单提交
  async function onSubmit(data: FormValues) {
    setIsSubmitting(true)
    try {
      await fetchPut(`/api/users-manager/${user.id}`, {
        name: data.name,
        email: data.email,
        avatar: data.avatar,
      })

      // 刷新会话以更新用户信息
      await refreshSession()

      toast.success(t("success.updateSuccess"))
      setIsEditing(false)
    } catch (error) {
      console.error("Error updating user settings:", error)
      toast.error(t("error.updateFailed"))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 取消编辑，重置表单
  function handleCancel() {
    form.reset({
      name: user.name || "",
      email: user.email || "",
      avatar: user.avatar || "",
    })
    setIsEditing(false)
  }

  // 获取用户名首字母作为头像占位符
  const getInitials = (name: string | null | undefined) => {
    if (!name) return "U"
    return name.charAt(0).toUpperCase()
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t("title")}</CardTitle>
          <CardDescription>
            管理您的个人信息和账户设置
          </CardDescription>
        </div>
        {!isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="flex items-center gap-1"
          >
            <Edit className="h-4 w-4" />
            {t("actions.edit")}
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="flex items-center gap-1"
            >
              <X className="h-4 w-4" />
              {t("actions.cancel")}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={form.handleSubmit(onSubmit)}
              disabled={isSubmitting}
              className="flex items-center gap-1"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSubmitting ? t("actions.saving") : t("actions.save")}
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
            {/* 只读模式 */}
            {!isEditing ? (
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Avatar className="h-20 w-20">
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={user.name || "用户头像"} />
                    ) : (
                      <AvatarFallback className="text-xl">
                        {getInitials(user.name)}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div>
                    <h3 className="text-xl font-medium">{user.name || "未设置姓名"}</h3>
                    <p className="text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                {user.promoCode && (
                  <div className="pt-4 border-t">
                    <div className="flex flex-col gap-1">
                      <span className="text-sm font-medium">{t("promoCode.label")}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-mono">{user.promoCode}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {t("promoCode.description")}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* 编辑模式 */
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="avatar"
                  render={({ field }) => (
                    <FormItem className="flex flex-col items-center gap-1">
                      <FormLabel className="self-start">{t("avatar.label")}</FormLabel>
                      <FormControl>
                        <UserAvatarUploader
                          value={field.value}
                          onChange={field.onChange}
                          userName={user.name || ""}
                        />
                      </FormControl>
                      <FormDescription className="self-start">
                        {t("avatar.description")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("name.label")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("name.placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("email.label")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("email.placeholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {user.promoCode && (
                  <div className="pt-4 border-t">
                    <div className="flex flex-col gap-1">
                      <span className="text-sm font-medium">{t("promoCode.readOnly")}</span>
                      <div className="flex items-center gap-2">
                        <Input
                          value={user.promoCode}
                          readOnly
                          className="font-mono bg-muted"
                        />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {t("promoCode.description")}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
