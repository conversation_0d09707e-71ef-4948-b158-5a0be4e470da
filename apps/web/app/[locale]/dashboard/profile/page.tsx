import { getUser } from "@lib/actions/users"
import { authUser } from "@repo/auth/server"
import { UserProfileForm } from "./components/UserProfileForm"

export default async function Page() {
	const auth = await authUser()
	const user = await getUser(auth?.id)

	if (!user) {
		return (
			<section className="max-w-3xl mx-auto p-4">
				<div className="bg-card rounded-lg p-6 text-center">
					<p className="text-muted-foreground">用户信息加载失败</p>
				</div>
			</section>
		)
	}

	return (
		<section className="max-w-3xl mx-auto p-4">
			<UserProfileForm user={user} />
		</section>
	)
}
