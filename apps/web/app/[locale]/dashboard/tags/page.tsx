import { getLanguageName, ProjectLanguage, Language } from "@repo/shared-types"
import { getByProjectId } from "@/lib/services/ProjectSiteSettingsServices"
import TagsContent from "./components/TagsContent"
import { authUser } from "@repo/auth/server"
import { redirect } from "next/navigation"
import { prisma } from "@repo/db"


export default async function TagsPage() {
  // 验证用户权限
  const auth = await authUser()
  if (!auth) {
    redirect("/login")
  }

  // 获取用户的项目列表
  const projects = await prisma.project.findMany({
    where: { userId: auth.id },
    orderBy: { createdAt: 'desc' }
  })

  if (!projects || projects.length === 0) {
    redirect("/dashboard")
  }

  // 使用第一个项目作为默认项目
  const project_id = projects[0]?.id || ""
  const project = await getByProjectId(project_id)

  const defaultLanguage = (project!.defaultLocale ||
    ProjectLanguage.EN) as ProjectLanguage
  const languageList = ((project?.languanges as Array<string>) ?? []).map(
    (code: string) => {
      return { code, name: getLanguageName(code) }
    },
  ) as Language[]

  return (
    <TagsContent
      defaultLanguage={defaultLanguage}
      languages={languageList}
    />
  )
}
