"use client"

import { useState } from "react"
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Textarea,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Button,
} from "@repo/ui/components"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Benefit } from "@repo/db"
import { fetchPost, fetchPut } from "@repo/utils/react"
import { toast } from "sonner"
import { BenefitCostRules } from "@repo/shared-types"

// 表单验证模式
const benefitFormSchema = z.object({
	code: z.string().min(1, "权益代码不能为空"),
	name: z.string().min(1, "权益名称不能为空"),
	description: z.string().optional(),
	cost: z.coerce.number().int().min(0, "消耗点数不能为负数"),
	costRules: z.string().optional(),
})

type BenefitFormValues = z.infer<typeof benefitFormSchema>

interface BenefitFormProps {
	benefit: Partial<Benefit>
	onCancel: () => void
	onSuccess: () => void
}

export default function BenefitForm({
	benefit,
	onCancel,
	onSuccess,
}: BenefitFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false)
	const isEditing = !!benefit.id

	// 初始化表单
	const form = useForm<BenefitFormValues>({
		resolver: zodResolver(benefitFormSchema),
		defaultValues: {
			code: benefit.code || "",
			name: benefit.name || "",
			description: benefit.description || "",
			cost: benefit.cost || 0,
			costRules: benefit.costRules || "",
		},
	})

	// 提交表单
	const onSubmit = async (values: BenefitFormValues) => {
		setIsSubmitting(true)

		try {
			if (isEditing) {
				// 更新现有权益
				await fetchPut(
					`/api/benefits-management/update?id=${benefit.id}`,
					values,
				)
				toast.success("权益更新成功")
			} else {
				// 创建新权益
				await fetchPost("/api/benefits-management", values)
				toast.success("权益创建成功")
			}

			onSuccess()
		} catch (error) {
			console.error("保存权益失败:", error)
			toast.error("保存权益失败")
		} finally {
			setIsSubmitting(false)
		}
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
				<FormField
					control={form.control}
					name="code"
					render={({ field }) => (
						<FormItem>
							<FormLabel>权益代码</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="输入权益代码"
									disabled={isEditing} // 编辑模式下不允许修改代码
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>权益名称</FormLabel>
							<FormControl>
								<Input {...field} placeholder="输入权益名称" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormLabel>描述</FormLabel>
							<FormControl>
								<Textarea {...field} placeholder="输入权益描述" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="cost"
					render={({ field }) => (
						<FormItem>
							<FormLabel>消耗点数</FormLabel>
							<FormControl>
								<Input
									{...field}
									type="number"
									min={0}
									placeholder="输入消耗点数"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="costRules"
					render={({ field }) => (
						<FormItem>
							<FormLabel>扣除规则</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="选择扣除规则" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value={BenefitCostRules.NO_COST}>
										无成本
									</SelectItem>
									<SelectItem value={BenefitCostRules.PRE_INCENTIVE}>
										预激励
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_ONCE}>
										一次性
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_UNIT}>
										按单位
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_COUNT}>
										按次数
									</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
						disabled={isSubmitting}
					>
						取消
					</Button>
					<Button type="submit" disabled={isSubmitting}>
						{isSubmitting ? "保存中..." : isEditing ? "更新" : "创建"}
					</Button>
				</div>
			</form>
		</Form>
	)
}
