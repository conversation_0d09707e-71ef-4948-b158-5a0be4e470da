"use client"

import { useState, useRef } from "react"
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@repo/ui/components"
// 直接导入当前目录下的组件
import BenefitTable, { BenefitTableRef } from "./BenefitTable"
import BenefitForm from "./BenefitForm"
import { Benefit } from "@repo/db"

export default function BenefitsManagementContent() {
	const [selectedBenefit, setSelectedBenefit] =
		useState<Partial<Benefit> | null>(null)
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const tableRef = useRef<BenefitTableRef>(null)

	// 处理选择权益进行编辑
	const handleEditBenefit = (benefit: Benefit) => {
		setSelectedBenefit(benefit)
		setIsDialogOpen(true)
	}

	// 处理创建新权益
	const handleCreateBenefit = () => {
		setSelectedBenefit({})
		setIsDialogOpen(true)
	}

	// 处理取消编辑
	const handleCancelEdit = () => {
		setIsDialogOpen(false)
		setSelectedBenefit(null)
	}

	// 处理保存成功
	const handleSaveSuccess = () => {
		setIsDialogOpen(false)
		setSelectedBenefit(null)
		// 刷新表格数据
		tableRef.current?.refresh()
	}

	return (
		<div className="container mx-auto p-6">
			<Card>
				<CardHeader>
					<CardTitle>权益列表</CardTitle>
				</CardHeader>
				<CardContent>
					<BenefitTable
						ref={tableRef}
						onEdit={handleEditBenefit}
						onCreate={handleCreateBenefit}
					/>
				</CardContent>
			</Card>

			{/* 权益表单对话框 */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[500px]">
					<DialogHeader>
						<DialogTitle>
							{selectedBenefit?.id ? "编辑权益" : "创建权益"}
						</DialogTitle>
					</DialogHeader>

					{selectedBenefit !== null && (
						<BenefitForm
							benefit={selectedBenefit}
							onCancel={handleCancelEdit}
							onSuccess={handleSaveSuccess}
						/>
					)}
				</DialogContent>
			</Dialog>
		</div>
	)
}
