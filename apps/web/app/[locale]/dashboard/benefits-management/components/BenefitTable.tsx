"use client"

import { useEffect, useState, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react"
import {
	DataTable,
	DataTableContent,
	DataTablePagination,
	DataTableToolbar,
	DataTableFilter,
	DataTableFilterField,
	DataTableFilterActions,
	Input,
	Select,
	SelectItem,
	Button,
	SelectContent,
	SelectTrigger,
	SelectValue,
} from "@repo/ui/components"
import { useTable } from "@repo/ui/hooks"
import { Edit, Trash, Search, X } from "lucide-react"
import { Benefit } from "@repo/db"
import { fetchGet, fetchDelete } from "@repo/utils/react"
import { toast } from "sonner"
import { BenefitCostRules } from "@repo/shared-types"
import { useConfirm } from "@repo/ui/hooks"

interface BenefitTableProps {
	onEdit: (benefit: Benefit) => void
	onCreate: () => void
}

export interface BenefitTableRef {
	refresh: () => void
}

// 过滤条件接口
interface FilterValues {
	code?: string
	name?: string
	costRules?: string
}

const BenefitTable = forwardRef<BenefitTableRef, BenefitTableProps>(
	({ onEdit, onCreate }, ref) => {
		const [benefits, setBenefits] = useState<Benefit[]>([])
		const [isLoading, setIsLoading] = useState(true)
		const [totalItems, setTotalItems] = useState(0)
		const [filters, setFilters] = useState<FilterValues>({
			code: "",
			name: "",
			costRules: "",
		})
		const { ConfirmDialog, confirm } = useConfirm()

		// 定义表格列
		const columns = [
			{
				id: "index",
				header: "序号",
				cell: ({ row }: { row: any }) => row.index + 1,
			},
			{
				accessorKey: "code",
				header: "权益代码",
			},
			{
				accessorKey: "name",
				header: "权益名称",
			},
			{
				accessorKey: "description",
				header: "描述",
				cell: ({ row }: { row: any }) => row.getValue("description") || "-",
			},
			{
				accessorKey: "cost",
				header: "消耗点数",
			},
			{
				accessorKey: "costRules",
				header: "扣除规则",
				cell: ({ row }: { row: any }) => {
					const costRule = row.getValue("costRules") as BenefitCostRules | null
					if (!costRule) return "-"

					const ruleMap = {
						[BenefitCostRules.NO_COST]: "无成本",
						[BenefitCostRules.PRE_INCENTIVE]: "预激励",
						[BenefitCostRules.PER_ONCE]: "一次性",
						[BenefitCostRules.PER_UNIT]: "按单位",
						[BenefitCostRules.PER_COUNT]: "按次数",
					}

					return ruleMap[costRule] || costRule
				},
			},
			{
				id: "actions",
				header: "操作",
				cell: ({ row }: { row: any }) => (
					<div className="flex gap-2">
						<button
							type="button"
							className="text-blue-600 hover:text-blue-800"
							onClick={() => onEdit(row.original as Benefit)}
						>
							<Edit size={16} />
						</button>
						<button
							type="button"
							className="text-red-600 hover:text-red-800"
							onClick={() => handleDelete(row.original.id)}
						>
							<Trash size={16} />
						</button>
					</div>
				),
			},
		]

		// 加载权益数据
		const loadBenefits = async (page = 1) => {
			setIsLoading(true)
			try {
				// 构建查询参数
				const params = new URLSearchParams({
					page: page.toString(),
					pageSize: pagination.pageSize.toString(),
				})

				// 添加过滤条件
				if (filters.code) params.append("code", filters.code)
				if (filters.name) params.append("name", filters.name)
				if (filters.costRules && filters.costRules !== "-")
					params.append("costRules", filters.costRules)

				const response = await fetchGet<{
					data: Benefit[]
					total: number
				}>(`/api/benefits-management?${params.toString()}`)

				if (response) {
					setBenefits(response.data)
					setTotalItems(response.total)
				}
			} catch (error) {
				console.error("加载权益失败:", error)
				toast.error("加载权益失败")
			} finally {
				setIsLoading(false)
			}
		}

		// 处理过滤提交
		const handleFilterSubmit = (values: FilterValues) => {
			setFilters(values)
			loadBenefits(1)
		}

		// 删除权益
		const handleDelete = async (id: string) => {
			const confirmed = await confirm({
				title: "确认删除",
				description: "确定要删除这个权益吗？此操作不可撤销。",
				confirmText: "删除",
				cancelText: "取消",
			})

			if (confirmed) {
				try {
					await fetchDelete(`/api/benefits-management/delete?id=${id}`)
					toast.success("权益删除成功")
					loadBenefits(pagination.page)
				} catch (error) {
					// console.error("删除权益失败:", error)
					toast.error(String(error) ?? "删除权益失败")
				}
			}
		}

		// 使用表格钩子
		const { table, contentProps, paginationProps, toolbarProps, pagination } =
			useTable({
				columns,
				data: benefits,
				manualPagination: true,
				totalItems,
				initialPage: 1,
				initialPageSize: 10,
				isLoading,
				onPageChange: (page) => {
					loadBenefits(page)
				},
				onRefresh: () => {
					loadBenefits(1)
				},
				onCreate,
				onCreateLabel: "创建权益",
			})

		// 初始加载和页码变化时重新加载数据
		useEffect(() => {
			loadBenefits(pagination.page)
		}, [pagination.page])

		// 暴露刷新方法给父组件
		useImperativeHandle(ref, () => ({
			refresh: () => loadBenefits(1),
		}))

		return (
			<>
				{ConfirmDialog}
				<DataTable table={table} isLoading={isLoading}>
					<DataTableFilter
						table={table}
						onSubmit={handleFilterSubmit}
						onReset={() => {
							setFilters({ code: "", name: "", costRules: "" })
							loadBenefits(1)
						}}
					>
						<DataTableFilterField name="code">
							<Input placeholder="权益代码" />
						</DataTableFilterField>

						<DataTableFilterField name="name">
							<Input placeholder="权益名称" />
						</DataTableFilterField>

						<DataTableFilterField name="costRules">
							<Select>
								<SelectTrigger className="w-full">
									<SelectValue placeholder="选择扣除规则" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value={BenefitCostRules.NO_COST}>
										无成本
									</SelectItem>
									<SelectItem value={BenefitCostRules.PRE_INCENTIVE}>
										预激励
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_ONCE}>
										一次性
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_UNIT}>
										按单位
									</SelectItem>
									<SelectItem value={BenefitCostRules.PER_COUNT}>
										按次数
									</SelectItem>
								</SelectContent>
							</Select>
						</DataTableFilterField>

						<DataTableFilterActions>
							<Button type="submit">筛选</Button>
							<Button type="reset" variant="outline">
								重置
							</Button>
						</DataTableFilterActions>
					</DataTableFilter>

					<DataTableToolbar {...toolbarProps} />

					<DataTableContent {...contentProps} />

					<DataTablePagination {...paginationProps} />
				</DataTable>
			</>
		)
	},
)

export default BenefitTable
