import { authUser } from "@repo/auth/server"
import { UserRole } from "@lib/consts"
import { NotLogin, NoPermission } from "@/app/not-found"
import { getTranslations, setRequestLocale } from "next-intl/server"
import type { Metadata } from "next"
import BenefitsManagementContent from "./components/BenefitsManagementContent"

type Props = {
	params: Promise<{ locale: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
	const { locale } = await params
	setRequestLocale(locale)
	const t = await getTranslations({ locale, namespace: "Dashboard" })
	return {
		title: t("benefitsManagement"),
		robots: { index: false, follow: false },
	}
}

export default async function BenefitsManagementPage() {
	const user = await authUser()

	// 检查用户是否登录
	if (!user) {
		return NotLogin()
	}

	// 检查用户是否为管理员
	if (user.role !== UserRole.Admin) {
		return NoPermission({ message: "只有管理员可以访问此页面" })
	}

	return <BenefitsManagementContent />
}
