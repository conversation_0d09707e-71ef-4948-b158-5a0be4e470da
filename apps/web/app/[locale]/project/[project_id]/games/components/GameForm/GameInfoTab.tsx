"use client"

import { useTranslations } from "next-intl"
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	Alert,
	AlertTitle,
	AlertDescription,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@repo/ui/components"
import { AlertCircle, Loader2 } from "lucide-react"
import { useGameInfoForm, GameInfoFormData } from "../../hooks/useGameInfoForm"

interface GameInfoTabProps {
	projectId: string
	gameId: string
}

export default function GameInfoTab({ projectId, gameId }: GameInfoTabProps) {
	const t = useTranslations("Games")

	const { form, isLoading, error, handleSubmit, isSaving } =
		useGameInfoForm({
			projectId,
			gameId,
		})

	const onSubmit = (data: GameInfoFormData) => {
		handleSubmit(data)
	}

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<Loader2 className="h-8 w-8 animate-spin" />
			</div>
		)
	}

	if (gameId === "new-game") {
		return (
			<Alert>
				<AlertCircle className="h-4 w-4" />
				<AlertTitle>提示</AlertTitle>
				<AlertDescription>
					请先保存游戏基本信息，然后再设置游戏详细信息。
				</AlertDescription>
			</Alert>
		)
	}

	return (
		<div className="space-y-6">
			{error && (
				<Alert variant="destructive">
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>错误</AlertTitle>
					<AlertDescription>{error}</AlertDescription>
				</Alert>
			)}

			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>游戏信息设置</CardTitle>
							<CardDescription>
								设置游戏的详细信息，这些信息将在游戏页面中显示
							</CardDescription>
						</div>
					</div>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="developer"
									render={({ field }) => (
										<FormItem>
											<FormLabel>开发者</FormLabel>
											<FormControl>
												<Input placeholder="请输入开发者名称" {...field} />
											</FormControl>
											<FormDescription>
												游戏的开发者或开发团队名称
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="version"
									render={({ field }) => (
										<FormItem>
											<FormLabel>版本</FormLabel>
											<FormControl>
												<Input placeholder="例如：1.0.0" {...field} />
											</FormControl>
											<FormDescription>游戏的当前版本号</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="releaseDate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>发布日期</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>游戏的首次发布日期</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="lastUpdate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>最后更新</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>游戏的最后更新日期</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="size"
									render={({ field }) => (
										<FormItem>
											<FormLabel>文件大小</FormLabel>
											<FormControl>
												<Input placeholder="例如：50MB" {...field} />
											</FormControl>
											<FormDescription>游戏文件的大小</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="platform"
									render={({ field }) => (
										<FormItem>
											<FormLabel>支持平台</FormLabel>
											<FormControl>
												<Input
													placeholder="例如：Windows, Mac, Android"
													{...field}
												/>
											</FormControl>
											<FormDescription>游戏支持的平台</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="ageRating"
									render={({ field }) => (
										<FormItem>
											<FormLabel>年龄评级</FormLabel>
											<FormControl>
												<Input
													placeholder="例如：全年龄, 12+, 18+"
													{...field}
												/>
											</FormControl>
											<FormDescription>游戏的年龄评级</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="rating"
									render={({ field }) => (
										<FormItem>
											<FormLabel>游戏评分</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="0"
													max="5"
													step="0.1"
													placeholder="4.5"
													{...field}
													onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
												/>
											</FormControl>
											<FormDescription>
												游戏评分 (0-5分，支持小数)
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
								<div className="max-w-6xl mx-auto flex justify-end w-full">
									<Button
										type="reset"
										variant="outline"
										disabled={isSaving}
										className="mr-3"
									>
										{t("reset")}
									</Button>
									<Button type="submit" loading={isSaving}>
										{isSaving ? t("saving") : t("saveSettings")}
									</Button>
								</div>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	)
}
