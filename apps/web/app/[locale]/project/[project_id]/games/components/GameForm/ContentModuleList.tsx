"use client"

import { useTranslations } from "next-intl"
import {
	<PERSON>ton,
	AlertDialog,
	AlertDialogTrigger,
	AlertDialogContent,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogCancel,
	AlertDialogAction,
} from "@repo/ui/components"
import { Trash2, GripVertical } from "lucide-react"
import { Icon } from "@/lib/components/icons/Icon"
import { cn } from "@repo/utils/react"
import { ContentModule } from "../../hooks/useGameContentsForm"
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
	DragEndEvent,
} from "@dnd-kit/core"
import {
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

interface ContentModuleListProps {
	modules: ContentModule[]
	selectedModuleId: string | null
	onSelectModule: (moduleId: string) => void
	onDeleteModule: (moduleId: string) => void
	onMoveModule: (moduleId: string, direction: "up" | "down" | number) => void
	onToggleEnabled: (moduleId: string) => void
}

// 可排序的模块项组件
function SortableModuleItem({
	module,
	selectedModuleId,
	onSelectModule,
	onDeleteModule,
	onToggleEnabled,
	t,
}: {
	module: ContentModule
	selectedModuleId: string | null
	onSelectModule: (id: string) => void
	onDeleteModule: (id: string) => void
	onToggleEnabled: (id: string) => void
	t: any
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: module.tabId })

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		zIndex: isDragging ? 10 : 1,
		opacity: isDragging ? 0.8 : 1,
	}

	// 获取模块类型图标
	const getModuleTypeIcon = (moduleTabId: string, customIcon?: string) => {
		// 如果有自定义图标，优先使用自定义图标
		if (customIcon) {
			if (customIcon.startsWith("http")) {
				// 如果是URL，返回图片元素
				return (
					<img src={customIcon} alt={moduleTabId} className="h-5 w-5 object-contain" />
				)
			} else {
				// 如果是图标名称，使用Icon组件
				return (
					<Icon
						name={customIcon}
						size={20}
					/>
				)
			}
		}

		// 默认图标映射
		const defaultIcon =
			{
				gameInfo: "info",
				gameplay: "gamepad",
				strategy: "lightbulb",
				screenshots: "image",
				videos: "video",
				faq: "help-circle",
				download: "download",
				updates: "refresh-cw",
				community: "users",
				userComments: "message-circle",
				coreFeatures: "star",
				recommendationReasons: "thumbs-up",
				platformFeatures: "monitor",
				custom: "file",
			}[moduleTabId] || "file"

		return (
			<Icon
				name={defaultIcon}
				size={20}
			/>
		)
	}

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"flex items-center justify-between p-3 rounded-md border cursor-pointer mb-2",
				selectedModuleId === module.tabId
					? "border-primary bg-primary/5"
					: "border-border",
				isDragging && "shadow-lg",
			)}
			onClick={() => onSelectModule(module.tabId)}
		>
			<div className="flex items-center space-x-3">
				<div
					className="cursor-grab text-muted-foreground hover:text-foreground"
					{...attributes}
					{...listeners}
				>
					<GripVertical className="h-4 w-4" />
				</div>
				<div className="flex items-center justify-center w-6 h-6">
					{getModuleTypeIcon(module.tabId, module.icon)}
				</div>
				<div>
					<div className="font-medium">{module.title}</div>
					<div className="text-xs text-muted-foreground">
						{t(`moduleTypes.${module.tabId}`)}
					</div>
				</div>
			</div>

			<div className="flex items-center space-x-2">
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button variant="ghost">
							<Trash2 className="h-4 w-4" />
						</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>{t("confirmDeleteModule")}</AlertDialogTitle>
							<AlertDialogDescription>
								{t("confirmDeleteModuleDescription")}
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>{t("cancel")}</AlertDialogCancel>
							<AlertDialogAction
								onClick={() => {
									onDeleteModule(module.tabId)
								}}
							>
								{t("confirm")}
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			</div>
		</div>
	)
}

export default function ContentModuleList({
	modules,
	selectedModuleId,
	onSelectModule,
	onDeleteModule,
	onMoveModule,
	onToggleEnabled,
}: ContentModuleListProps) {
	const t = useTranslations("Games")

	// 过滤掉已标记为删除的模块，并按顺序排序
	const sortedModules = [...modules]
		.filter(module => module.operation !== 'delete')

	// 设置传感器
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	)

	// 处理拖拽结束
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event

		if (over && active.id !== over.id) {
			// 找到正确的索引，使用 tabId 作为标识符
			const oldIndex = sortedModules.findIndex(
				(module) => module.tabId === active.id,
			)
			const newIndex = sortedModules.findIndex(
				(module) => module.tabId === over.id,
			)

			if (oldIndex !== -1 && newIndex !== -1) {
				// 直接将模块ID和目标索引传递给移动函数
				// 这里使用数字索引而不是方向，允许任意位置移动
				onMoveModule(active.id as string, newIndex)
			}
		}
	}

	return (
		<DndContext
			sensors={sensors}
			collisionDetection={closestCenter}
			onDragEnd={handleDragEnd}
		>
			<SortableContext
				items={sortedModules.map((module) => module.tabId)}
				strategy={verticalListSortingStrategy}
			>
				<div className="space-y-2">
					{sortedModules.map((module) => (
						<SortableModuleItem
							key={module.tabId}
							module={module}
							selectedModuleId={selectedModuleId}
							onSelectModule={onSelectModule}
							onDeleteModule={onDeleteModule}
							onToggleEnabled={onToggleEnabled}
							t={t}
						/>
					))}
				</div>
			</SortableContext>
		</DndContext>
	)
}
