"use client"

import {
	Button,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
	Input,
	Label,
	Textarea
} from "@repo/ui/components"
import { Plus, Trash2, HelpCircle } from "lucide-react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useEffect, useCallback, useMemo } from "react"

// FAQ项目类型定义
export interface FAQItem {
	question: string
	answer: string
}

// 表单验证模式
const faqFormSchema = z.object({
	faqs: z.array(
		z.object({
			question: z.string().min(1, "问题不能为空"),
			answer: z.string().min(1, "答案不能为空"),
		})
	),
})

type FAQFormData = z.infer<typeof faqFormSchema>

interface FAQEditorProps {
	faqs: FAQItem[]
	onChange: (faqs: FAQItem[]) => void
}

export default function FAQEditor({ faqs, onChange }: FAQEditorProps) {
	// 设置表单
	const form = useForm<FAQFormData>({
		resolver: zodResolver(faqFormSchema),
		defaultValues: {
			faqs: faqs.length > 0 ? faqs : [{ question: "", answer: "" }],
		},
	})

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "faqs",
	})

	// 当外部faqs数据变化时更新表单
	useEffect(() => {
		const newFaqs = faqs.length > 0 ? faqs : [{ question: "", answer: "" }]
		form.reset({ faqs: newFaqs })
	}, [faqs, form])

	// 创建防抖函数，避免频繁调用
	const debouncedNotifyChange = useMemo(() => {
		let timeoutId: NodeJS.Timeout | null = null

		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId)
			}

			timeoutId = setTimeout(() => {
				const currentFaqs = form.getValues("faqs")
				if (!Array.isArray(currentFaqs)) return

				// 过滤掉完全空的FAQ项目
				const validFaqs = currentFaqs.filter(faq => {
					if (!faq || typeof faq !== 'object') return false
					const question = (faq.question || "").trim()
					const answer = (faq.answer || "").trim()
					return question || answer
				})

				onChange(validFaqs)
			}, 300) // 300ms 防抖延迟
		}
	}, [form, onChange])

	// 立即通知变化的函数（用于添加/删除操作）
	const notifyChangeImmediate = useCallback(() => {
		const currentFaqs = form.getValues("faqs")
		if (!Array.isArray(currentFaqs)) return

		// 过滤掉完全空的FAQ项目
		const validFaqs = currentFaqs.filter(faq => {
			if (!faq || typeof faq !== 'object') return false
			const question = (faq.question || "").trim()
			const answer = (faq.answer || "").trim()
			return question || answer
		})

		onChange(validFaqs)
	}, [form, onChange])

	// 添加新的FAQ项目
	const addFAQ = () => {
		append({ question: "", answer: "" })
		// 立即通知变化，确保UI更新
		setTimeout(notifyChangeImmediate, 0)
	}

	// 删除FAQ项目
	const removeFAQ = (index: number) => {
		if (fields.length > 1) {
			remove(index)
			// 立即通知变化，确保UI更新
			setTimeout(notifyChangeImmediate, 0)
		}
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<Label className="text-base font-medium">常见问题</Label>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={addFAQ}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					添加问题
				</Button>
			</div>

			<div className="space-y-3">
				{fields.map((field, index) => (
					<div
						key={field.id}
						className="border rounded-lg p-4 space-y-3 bg-card"
					>
						{/* 问题和答案标题行 */}
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<HelpCircle className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">问题 {index + 1}</span>
							</div>
							{fields.length > 1 && (
								<Button
									type="button"
									variant="ghost"
									size="icon"
									onClick={() => removeFAQ(index)}
									className="h-8 w-8 text-muted-foreground hover:text-destructive"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							)}
						</div>

						{/* 问题输入框 */}
						<FormField
							control={form.control}
							name={`faqs.${index}.question`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`question-${index}`} className="text-sm font-medium">
										问题
									</Label>
									<FormControl>
										<Input
											{...field}
											id={`question-${index}`}
											placeholder="请输入问题..."
											className="w-full"
											onChange={(e) => {
												field.onChange(e)
												// 使用防抖函数，避免过于频繁的更新
												debouncedNotifyChange()
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* 答案输入框 */}
						<FormField
							control={form.control}
							name={`faqs.${index}.answer`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`answer-${index}`} className="text-sm font-medium">
										答案
									</Label>
									<FormControl>
										<Textarea
											{...field}
											id={`answer-${index}`}
											placeholder="请输入答案..."
											className="w-full min-h-[80px] resize-none"
											rows={3}
											onChange={(e) => {
												field.onChange(e)
												// 使用防抖函数，避免过于频繁的更新
												debouncedNotifyChange()
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				))}
			</div>

			{fields.length === 0 && (
				<div className="text-center py-8 text-muted-foreground">
					<HelpCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
					<p>暂无FAQ项目</p>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={addFAQ}
						className="mt-2"
					>
						添加第一个问题
					</Button>
				</div>
			)}

			<p className="text-xs text-muted-foreground">
				FAQ内容将以问答形式展示在游戏详情页面中，帮助用户快速了解游戏相关信息。
			</p>
		</div>
	)
}
