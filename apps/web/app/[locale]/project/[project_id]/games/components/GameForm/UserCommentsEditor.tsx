"use client"

import {
	Button,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
	Input,
	Label,
	Textarea,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@repo/ui/components"
import { Plus, Trash2, MessageCircle, Star } from "lucide-react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useEffect, useCallback, useMemo } from "react"
import { cn } from "@repo/ui/utils"

// 用户评论项目类型定义
export interface UserCommentItem {
	name: string
	rating: number
	content: string
	publishDate: string
}

// 表单验证模式
const userCommentsFormSchema = z.object({
	comments: z.array(
		z.object({
			name: z.string().min(1, "评论者姓名不能为空"),
			rating: z.number().min(1, "评分不能少于1分").max(5, "评分不能超过5分"),
			content: z.string().min(1, "评论内容不能为空"),
			publishDate: z.string().min(1, "发布日期不能为空"),
		})
	),
})

type UserCommentsFormData = z.infer<typeof userCommentsFormSchema>

interface UserCommentsEditorProps {
	comments: UserCommentItem[]
	onChange: (comments: UserCommentItem[]) => void
}

export default function UserCommentsEditor({ comments, onChange }: UserCommentsEditorProps) {
	// 设置表单
	const form = useForm<UserCommentsFormData>({
		resolver: zodResolver(userCommentsFormSchema),
		defaultValues: {
			comments: comments.length > 0 ? comments : [{
				name: "",
				rating: 5,
				content: "",
				publishDate: new Date().toISOString().split('T')[0] || ""
			}],
		},
	})

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "comments",
	})

	// 当外部comments数据变化时更新表单
	useEffect(() => {
		const newComments = comments.length > 0 ? comments : [{
			name: "",
			rating: 5,
			content: "",
			publishDate: new Date().toISOString().split('T')[0] || ""
		}]
		form.reset({ comments: newComments })
	}, [comments, form])

	// 创建防抖函数，避免频繁调用
	const debouncedNotifyChange = useMemo(() => {
		let timeoutId: NodeJS.Timeout | null = null

		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId)
			}

			timeoutId = setTimeout(() => {
				const currentComments = form.getValues("comments")
				if (!Array.isArray(currentComments)) return

				// 直接传递所有评论，包括空的评论项目
				// 这样可以确保新添加的空评论也能被保存
				onChange(currentComments)
			}, 300) // 300ms 防抖延迟
		}
	}, [form, onChange])

	// 立即通知变化的函数（用于添加/删除操作）
	const notifyChangeImmediate = useCallback(() => {
		const currentComments = form.getValues("comments")
		if (!Array.isArray(currentComments)) return

		onChange(currentComments)
	}, [form, onChange])

	// 添加新的评论项目
	const addComment = () => {
		append({
			name: "",
			rating: 5,
			content: "",
			publishDate: new Date().toISOString().split('T')[0] || ""
		})
		// 立即通知变化，确保表单数据已更新
		setTimeout(notifyChangeImmediate, 0)
	}

	// 删除评论项目
	const removeComment = (index: number) => {
		if (fields.length > 1) {
			remove(index)
			// 立即通知变化，确保表单数据已更新
			setTimeout(notifyChangeImmediate, 0)
		}
	}

	// 渲染星级评分
	const renderStarRating = (rating: number) => {
		return (
			<div className="flex items-center gap-1">
				{[1, 2, 3, 4, 5].map((star) => (
					<Star
						key={star}
						className={cn(
							"h-4 w-4",
							star <= rating
								? "fill-yellow-400 text-yellow-400"
								: "text-muted-foreground"
						)}
					/>
				))}
			</div>
		)
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<Label className="text-base font-medium">用户评论</Label>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={addComment}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					添加评论
				</Button>
			</div>

			<div className="space-y-4">
				{fields.map((field, index) => (
					<div
						key={field.id}
						className="border rounded-lg p-4 space-y-4 bg-card"
					>
						{/* 评论标题行 */}
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<MessageCircle className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">评论 {index + 1}</span>
							</div>
							{fields.length > 1 && (
								<Button
									type="button"
									variant="ghost"
									size="icon"
									onClick={() => removeComment(index)}
									className="h-8 w-8 text-muted-foreground hover:text-destructive"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							)}
						</div>

						<div className="grid md:grid-cols-2 gap-4">
							{/* 评论者姓名 */}
							<FormField
								control={form.control}
								name={`comments.${index}.name`}
								render={({ field }) => (
									<FormItem>
										<Label htmlFor={`name-${index}`} className="text-sm font-medium">
											评论者姓名
										</Label>
										<FormControl>
											<Input
												{...field}
												value={field.value || ""}
												id={`name-${index}`}
												placeholder="请输入评论者姓名..."
												className="w-full"
												onChange={(e) => {
													field.onChange(e)
													// 使用防抖函数，避免过于频繁的更新
													debouncedNotifyChange()
												}}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* 发布日期 */}
							<FormField
								control={form.control}
								name={`comments.${index}.publishDate`}
								render={({ field }) => (
									<FormItem>
										<Label htmlFor={`publishDate-${index}`} className="text-sm font-medium">
											发布日期
										</Label>
										<FormControl>
											<Input
												{...field}
												value={field.value || ""}
												id={`publishDate-${index}`}
												type="date"
												className="w-full"
												onChange={(e) => {
													field.onChange(e)
													// 使用防抖函数，避免过于频繁的更新
													debouncedNotifyChange()
												}}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* 评分选择 */}
						<FormField
							control={form.control}
							name={`comments.${index}.rating`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`rating-${index}`} className="text-sm font-medium">
										评分 (1-5分)
									</Label>
									<div className="flex items-center gap-4">
										<Select
											value={(field.value || 5).toString()}
											onValueChange={(value) => {
												field.onChange(parseInt(value))
												// 使用防抖函数，避免过于频繁的更新
												debouncedNotifyChange()
											}}
										>
											<SelectTrigger className="w-32">
												<SelectValue placeholder="选择评分" />
											</SelectTrigger>
											<SelectContent>
												{[1, 2, 3, 4, 5].map((rating) => (
													<SelectItem key={rating} value={rating.toString()}>
														{rating} 分
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										{renderStarRating(field.value || 5)}
									</div>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* 评论内容 */}
						<FormField
							control={form.control}
							name={`comments.${index}.content`}
							render={({ field }) => (
								<FormItem>
									<Label htmlFor={`content-${index}`} className="text-sm font-medium">
										评论内容
									</Label>
									<FormControl>
										<Textarea
											{...field}
											value={field.value || ""}
											id={`content-${index}`}
											placeholder="请输入评论内容..."
											className="w-full min-h-[100px] resize-none"
											rows={4}
											onChange={(e) => {
												field.onChange(e)
												// 使用防抖函数，避免过于频繁的更新
												debouncedNotifyChange()
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				))}
			</div>

			{fields.length === 0 && (
				<div className="text-center py-8 text-muted-foreground">
					<MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
					<p>暂无用户评论</p>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={addComment}
						className="mt-2"
					>
						添加第一条评论
					</Button>
				</div>
			)}

			<p className="text-xs text-muted-foreground">
				用户评论将展示在游戏详情页面中，帮助其他用户了解游戏的真实评价和体验。
			</p>
		</div>
	)
}
