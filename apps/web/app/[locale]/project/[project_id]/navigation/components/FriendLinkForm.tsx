"use client"

import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
	Input,
	Button,
} from "@repo/ui/components"
import { Check, PlusIcon, Pencil, Trash2, X } from "lucide-react"
import { useFriendLinksForm } from "../hooks/useFriendLinksForm"

interface FriendLinkFormProps {
	projectId: string
}

export default function FriendLinkForm({ projectId }: FriendLinkFormProps) {
	const {
		form,
		fields,
		isSaving,
		isLoading,
		editingIndices,
		addFriendLink,
		startEditing,
		cancelEditing,
		deleteFriendLink,
		saveFriendLink,
		onSubmit,
	} = useFriendLinksForm({ projectId })

	// 处理删除按钮点击 - 直接删除，不需要确认
	const handleDeleteClick = async (index: number) => {
		await deleteFriendLink(index)
	}

	// 判断是否正在编辑某个链接
	const isEditing = (index: number) => editingIndices.has(index)

	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<h3 className="text-lg font-medium">友情链接</h3>
				<Button
					onClick={addFriendLink}
					size="sm"
					className="flex items-center gap-1"
				>
					<PlusIcon className="h-4 w-4" />
					<span>添加链接</span>
				</Button>
			</div>

			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
					{isLoading ? (
						<div className="py-4 text-center text-muted-foreground">
							加载中...
						</div>
					) : (
						<div className="grid gap-2">
							{fields.map((field, index) => (
								<div
									key={field.id}
									className="flex items-center justify-between p-3 border rounded-md"
								>
									{isEditing(index) ? (
										<div className="flex-1 flex items-center gap-3">
											<FormField
												control={form.control}
												name={`links.${index}.name`}
												render={({ field }) => (
													<FormItem className="max-w-[220px]">
														<FormControl>
															<Input placeholder="请输入链接名称" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`links.${index}.url`}
												render={({ field }) => (
													<FormItem className="w-full">
														<FormControl>
															<Input placeholder="请输入链接地址" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									) : (
										<div className="flex-1">
											<div className="font-medium">{field.name}</div>
											<div className="text-sm text-muted-foreground truncate">
												{field.url}
											</div>
										</div>
									)}
									<div className="flex gap-2 ml-3">
										{isEditing(index) ? (
											<>
												<Button
													variant="ghost"
													size="icon"
													type="button"
													onClick={() => saveFriendLink(index)}
												>
													<Check className="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													type="button"
													onClick={() => cancelEditing(index)}
												>
													<X className="h-4 w-4" />
												</Button>
											</>
										) : (
											<>
												<Button
													variant="ghost"
													size="icon"
													type="button"
													onClick={() => startEditing(index)}
												>
													<Pencil className="h-4 w-4" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													type="button"
													onClick={() => handleDeleteClick(index)}
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</>
										)}
									</div>
								</div>
							))}
						</div>
					)}

					<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
						<div className="max-w-6xl mx-auto flex justify-end w-full">
							<Button
								type="button"
								variant="outline"
								className="mr-3"
								disabled={isSaving}
								onClick={() => form.reset()}
							>
								重置
							</Button>
							<Button type="submit" disabled={isSaving}>
								{isSaving ? "保存中..." : "保存设置"}
							</Button>
						</div>
					</div>
				</form>
			</Form>
		</div>
	)
}
