"use client"

import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Button,
	Separator,
} from "@repo/ui/components"
import {
	Mail,
	MessageSquare,
	Share2,
	Image as ImageIcon,
	Video,
	Link2,
} from "lucide-react"
import { useContactInfoForm } from "../hooks/useContactInfoForm"

interface ContactInfoFormProps {
	projectId: string
}

export default function ContactInfoForm({ projectId }: ContactInfoFormProps) {
	const { form, isSaving, isLoading, onSubmit } = useContactInfoForm({
		projectId,
	})

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h3 className="text-lg font-medium">联系信息</h3>
			</div>

			{isLoading ? (
				<div className="py-4 text-center text-muted-foreground">加载中...</div>
			) : (
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<div className="space-y-2">
							<FormField
								control={form.control}
								name="contactEmail"
								render={({ field }) => (
									<FormItem>
										<FormLabel>联系邮箱</FormLabel>
										<div className="flex items-center">
											<div className="w-10 text-center text-muted-foreground">
												<Mail className="h-4 w-4 mx-auto" />
											</div>
											<div className="flex-1">
												<FormControl>
													<Input placeholder="请输入联系邮箱" {...field} />
												</FormControl>
												<FormMessage />
											</div>
										</div>
										<p className="text-xs text-muted-foreground mt-1">
											用于接收系统通知和用户反馈，同时将显示在隐私政策、用户协议和Cookie协议中
										</p>
									</FormItem>
								)}
							/>
						</div>

						<Separator className="my-4" />
						<h4 className="text-sm font-medium mb-3">社交媒体链接</h4>

						<FormField
							control={form.control}
							name="twitter"
							render={({ field }) => (
								<FormItem>
									<div className="flex items-center">
										<div className="w-10 text-center text-muted-foreground">
											<MessageSquare className="h-4 w-4 mx-auto" />
										</div>
										<div className="flex-1">
											<FormControl>
												<Input placeholder="Twitter链接" {...field} />
											</FormControl>
											<FormMessage />
										</div>
									</div>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="facebook"
							render={({ field }) => (
								<FormItem>
									<div className="flex items-center">
										<div className="w-10 text-center text-muted-foreground">
											<Share2 className="h-4 w-4 mx-auto" />
										</div>
										<div className="flex-1">
											<FormControl>
												<Input placeholder="Facebook链接" {...field} />
											</FormControl>
											<FormMessage />
										</div>
									</div>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="instagram"
							render={({ field }) => (
								<FormItem>
									<div className="flex items-center">
										<div className="w-10 text-center text-muted-foreground">
											<ImageIcon className="h-4 w-4 mx-auto" />
										</div>
										<div className="flex-1">
											<FormControl>
												<Input placeholder="Instagram链接" {...field} />
											</FormControl>
											<FormMessage />
										</div>
									</div>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="youtube"
							render={({ field }) => (
								<FormItem>
									<div className="flex items-center">
										<div className="w-10 text-center text-muted-foreground">
											<Video className="h-4 w-4 mx-auto" />
										</div>
										<div className="flex-1">
											<FormControl>
												<Input placeholder="Youtube链接" {...field} />
											</FormControl>
											<FormMessage />
										</div>
									</div>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="linkedin"
							render={({ field }) => (
								<FormItem>
									<div className="flex items-center">
										<div className="w-10 text-center text-muted-foreground">
											<Link2 className="h-4 w-4 mx-auto" />
										</div>
										<div className="flex-1">
											<FormControl>
												<Input placeholder="LinkedIn链接" {...field} />
											</FormControl>
											<FormMessage />
										</div>
									</div>
								</FormItem>
							)}
						/>

						<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
							<div className="max-w-6xl mx-auto flex justify-end w-full">
								<Button
									type="reset"
									variant="outline"
									className="mr-3"
									disabled={isSaving}
								>
									重置
								</Button>
								<Button type="submit" disabled={isSaving}>
									{isSaving ? "保存中..." : "保存设置"}
								</Button>
							</div>
						</div>
					</form>
				</Form>
			)}
		</div>
	)
}
