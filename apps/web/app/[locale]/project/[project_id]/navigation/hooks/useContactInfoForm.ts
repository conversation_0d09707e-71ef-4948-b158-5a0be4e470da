"use client"

import { useState, useEffect } from "react"
import { SocialLinksConfig } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { toast } from "sonner"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import useSWR from "swr"

// 定义表单验证模式
export const contactInfoFormSchema = z.object({
  contactEmail: z.string().email({ message: "请输入有效的邮箱地址" }).or(z.literal("")),
  twitter: z.string().url({ message: "请输入有效的URL" }).or(z.literal("")),
  facebook: z.string().url({ message: "请输入有效的URL" }).or(z.literal("")),
  instagram: z.string().url({ message: "请输入有效的URL" }).or(z.literal("")),
  youtube: z.string().url({ message: "请输入有效的URL" }).or(z.literal("")),
  linkedin: z.string().url({ message: "请输入有效的URL" }).or(z.literal("")),
})

export type ContactInfoFormData = z.infer<typeof contactInfoFormSchema>

interface UseContactInfoFormProps {
  projectId: string
}

export function useContactInfoForm({ projectId }: UseContactInfoFormProps) {
  const [isSaving, setIsSaving] = useState(false)

  // 使用SWR获取站点设置数据
  const {
    data: siteSettings,
    error,
    isLoading,
    mutate: refreshData,
  } = useSWR<any>(`/api/project-site-settings?projectId=${projectId}`, fetchGet, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    onError: (err) => {
      console.error("获取站点设置失败:", err)
      toast.error("获取站点设置失败")
    },
  })

  // 设置表单
  const form = useForm<ContactInfoFormData>({
    resolver: zodResolver(contactInfoFormSchema),
    defaultValues: {
      contactEmail: "",
      twitter: "",
      facebook: "",
      instagram: "",
      youtube: "",
      linkedin: "",
    },
  })

  // 当数据加载完成后更新表单
  useEffect(() => {
    if (siteSettings) {
      form.reset({
        contactEmail: siteSettings.contactEmail || "",
        twitter: siteSettings.socialLinks?.twitter || "",
        facebook: siteSettings.socialLinks?.facebook || "",
        instagram: siteSettings.socialLinks?.instagram || "",
        youtube: siteSettings.socialLinks?.youtube || "",
        linkedin: siteSettings.socialLinks?.linkedin || "",
      })
    }
  }, [siteSettings, form])

  // 保存联系信息
  const saveContactInfo = async (data: ContactInfoFormData) => {
    setIsSaving(true)
    try {
      // 提取联系邮箱和社交媒体链接
      const { contactEmail, ...socialLinks } = data

      // 准备要保存的站点设置数据
      const updatedSettings = {
        ...siteSettings,
        contactEmail,
        socialLinks,
      }

      // 发送请求保存数据
      const response = await fetchPost("/api/project-site-settings", {
        projectId,
        siteSettings: updatedSettings,
      })

      if (response) {
        toast.success("联系信息保存成功")
        // 刷新SWR缓存
        refreshData()
        return true
      } else {
        throw new Error("保存失败")
      }
    } catch (error) {
      console.error("保存联系信息失败:", error)
      toast.error("保存联系信息失败")
      return false
    } finally {
      setIsSaving(false)
    }
  }

  // 提交表单
  const onSubmit = async (data: ContactInfoFormData) => {
    try {
      await saveContactInfo(data)
    } catch (error) {
      console.error("提交表单失败:", error)
      toast.error("提交表单失败")
    }
  }

  return {
    form,
    isSaving,
    isLoading,
    onSubmit,
  }
}
