"use client"

import { useState, useEffect } from "react"
import { FriendLink } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { toast } from "sonner"
import { z } from "zod"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import useSWR from "swr"

// 定义表单验证模式
export const friendLinkSchema = z.object({
	name: z.string().min(1, { message: "链接名称不能为空" }),
	url: z.string().url({ message: "请输入有效的URL" }),
})

export const friendLinksFormSchema = z.object({
	links: z.array(friendLinkSchema),
})

export type FriendLinkData = z.infer<typeof friendLinkSchema>
export type FriendLinksFormData = z.infer<typeof friendLinksFormSchema>

interface UseFriendLinksFormProps {
	projectId: string
}

export function useFriendLinksForm({ projectId }: UseFriendLinksFormProps) {
	const [isSaving, setIsSaving] = useState(false)
	const [editingIndices, setEditingIndices] = useState<Set<number>>(new Set())
	// 设置表单
	const form = useForm<FriendLinksFormData>({
		resolver: zodResolver(friendLinksFormSchema),
		defaultValues: {
			links: [],
		},
	})

	const { fields, append, remove, update } = useFieldArray({
		control: form.control,
		name: "links",
	})

	// 使用SWR获取友情链接数据
	const {
		data: siteSettings,
		isLoading,
		mutate: refreshData,
	} = useSWR<any>(
		`/api/project-site-settings?projectId=${projectId}`,
		fetchGet,
		{
			refreshInterval: 0,
			revalidateOnMount: true,
			revalidateOnFocus: true, // 修改为true，使得在tab切换时重新获取数据
			revalidateOnReconnect: false,
			onError: (err) => {
				console.error("获取站点设置失败:", err)
				toast.error("获取站点设置失败")
			},
		},
	)

	// 当数据加载完成后更新友情链接列表
	useEffect(() => {
		if (siteSettings) {
			// 如果没有友情链接，使用默认示例，但不自动保存
			if (!siteSettings.friendLinks || siteSettings.friendLinks.length === 0) {
				form.reset({
					links: [
						{
							name: "七只鹿",
							url: "https://www.qizhilu.com",
						},
					],
				})
			} else {
				form.reset({ links: siteSettings.friendLinks })
			}
		}
	}, [siteSettings, form])

	// 保存设置到服务器
	const saveSettingsToServer = async (
		updatedSettings: any,
		isInitial = false,
	) => {
		try {
			// 发送请求保存数据
			const response = await fetchPost("/api/project-site-settings", {
				projectId,
				siteSettings: updatedSettings,
			})

			if (response) {
				if (!isInitial) {
					toast.success("友情链接保存成功")
				} else {
					console.log("默认友情链接保存成功")
				}
				// 刷新SWR缓存
				refreshData()
				return true
			} else {
				throw new Error("保存失败")
			}
		} catch (error) {
			console.error("保存友情链接失败:", error)
			if (!isInitial) {
				toast.error("保存友情链接失败")
			}
			return false
		}
	}

	// 添加新友情链接
	const addFriendLink = () => {
		append({ name: "", url: "" })
		// 设置新添加的链接为编辑状态
		setTimeout(() => {
			setEditingIndices(new Set([...editingIndices, fields.length]))
		}, 0)
	}

	// 开始编辑友情链接
	const startEditing = (index: number) => {
		setEditingIndices(new Set([...editingIndices, index]))
	}

	// 取消编辑友情链接
	const cancelEditing = (index: number) => {
		const newEditingIndices = new Set(editingIndices)
		newEditingIndices.delete(index)
		setEditingIndices(newEditingIndices)

		// 如果是新添加的空链接，则删除
		const formValues = form.getValues()
		const link = formValues.links?.[index]
		if (link && !link.name && !link.url) {
			remove(index)
		} else {
			// 重置为原始值
			const originalLink = siteSettings?.friendLinks?.[index]
			if (originalLink) {
				update(index, originalLink)
			}
		}
	}

	// 删除友情链接
	const deleteFriendLink = async (index: number) => {
		try {
			remove(index)
			return true
		} catch (error) {
			console.error("删除友情链接失败:", error)
			toast.error("删除友情链接失败")
			return false
		}
	}

	// 完成单个友情链接编辑
	const saveFriendLink = async (index: number) => {
		try {
			await form.trigger(`links.${index}`)
			const linkErrors = form.formState.errors.links?.[index]

			if (linkErrors) {
				return false
			}

			const newEditingIndices = new Set(editingIndices)
			newEditingIndices.delete(index)
			setEditingIndices(newEditingIndices)

			return true
		} catch (error) {
			console.error("验证友情链接失败:", error)
			toast.error("验证友情链接失败")
			return false
		}
	}

	// 保存所有友情链接
	const saveFriendLinks = async () => {
		setIsSaving(true)
		try {
			// 获取当前表单值
			const formData = form.getValues()

			// 准备要保存的站点设置数据
			const updatedSettings = {
				...siteSettings,
				friendLinks: formData.links,
			}

			// 使用统一的保存函数
			const result = await saveSettingsToServer(updatedSettings)
			return result
		} catch (error) {
			console.error("保存友情链接失败:", error)
			toast.error("保存友情链接失败")
			return false
		} finally {
			setIsSaving(false)
		}
	}

	// 提交整个表单
	const onSubmit = async () => {
		try {
			// 直接保存
			const success = await saveFriendLinks()
			if (success) {
				// 清除所有编辑状态
				setEditingIndices(new Set())
			}
		} catch (error) {
			console.error("提交表单失败:", error)
			toast.error("提交表单失败")
		}
	}

	return {
		form,
		fields,
		isSaving,
		isLoading,
		editingIndices,
		addFriendLink,
		startEditing,
		cancelEditing,
		deleteFriendLink,
		saveFriendLink,
		saveFriendLinks,
		onSubmit,
	}
}
