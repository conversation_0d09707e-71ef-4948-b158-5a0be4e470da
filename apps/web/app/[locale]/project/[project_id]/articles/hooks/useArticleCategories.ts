"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { fetchGet } from "@repo/utils/react"
import { useEffect, useRef, useState } from "react"
import useSWR from "swr"
import { GameCategory, ProjectLocaleSiteSettingType } from "@repo/shared-types"

/**
 * 获取文章分类管理的Hook
 */
export function useArticleCategories(projectId: string) {
	// 使用 LanguageContext 获取语言信息
	const { defaultLanguage, currentLanguage } = useTranslationContext()

	// 加载状态
	const [isLoading, setIsLoading] = useState<boolean>(true)

	// 存储默认语言的分类数据
	const [defaultLanguageCategories, setDefaultLanguageCategories] = useState<
		GameCategory[]
	>([])

	// 标记是否已加载默认语言数据
	const hasLoadedDefaultLanguage = useRef(false)

	// 跟踪上一次请求的语言
	const [lastRequestedLanguage, setLastRequestedLanguage] = useState<
		string | null
	>(null)

	// 获取默认语言的分类数据
	const { mutate: refreshDefaultCategories } = useSWR<any>(
		defaultLanguage
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${defaultLanguage}&type=${ProjectLocaleSiteSettingType.ArticleCategories}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				const defaultCategories = data?.content || []
				setDefaultLanguageCategories(defaultCategories)
				hasLoadedDefaultLanguage.current = true

				// 如果当前语言就是默认语言，直接设置加载完成
				if (currentLanguage === defaultLanguage) {
					setIsLoading(false)
				}
			},
			onError: (error) => {
				console.error("Failed to load default language categories:", error)
				setIsLoading(false)
			},
		},
	)

	// 使用 useSWR 加载当前语言的分类数据（仅当不是默认语言时）
	const { isValidating } = useSWR<any>(
		currentLanguage &&
			currentLanguage !== defaultLanguage &&
			hasLoadedDefaultLanguage.current
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${currentLanguage}&type=${ProjectLocaleSiteSettingType.ArticleCategories}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: () => {
				// 请求成功后更新上一次请求的语言
				setLastRequestedLanguage(currentLanguage)
				setIsLoading(false)
			},
			onError: (error) => {
				console.error("Failed to load current language categories:", error)
				setIsLoading(false)
			},
		},
	)

	// 当语言变化或验证状态变化时更新加载状态
	useEffect(() => {
		// 当语言变化时设置加载状态
		if (currentLanguage && currentLanguage !== lastRequestedLanguage) {
			setIsLoading(true)

			// 如果已经加载了默认语言数据，设置为非加载状态
			if (hasLoadedDefaultLanguage.current) {
				setIsLoading(false)
			}
		}
		// 如果不在验证中，且上一次请求的语言与当前语言相同，则设置为非加载状态
		else if (!isValidating && lastRequestedLanguage === currentLanguage) {
			setIsLoading(false)
		}
	}, [currentLanguage, lastRequestedLanguage, defaultLanguage, isValidating])

	return {
		isLoading,
		categories: defaultLanguageCategories, // 始终返回默认语言的分类列表
		refreshCategories: refreshDefaultCategories,
	}
}
