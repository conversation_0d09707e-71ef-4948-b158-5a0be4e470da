"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { fetchGet, fetchPost, fetchDelete } from "@repo/utils/react"
import { toast } from "sonner"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Checkbox,
} from "@repo/ui/components"
import { RefreshCw, Trash2, RotateCcw, Eye, Filter } from "lucide-react"
import { BACKGROUND_TASK_STATUS, BACKGROUND_TASK_TYPE, BackgroundTaskStatus } from "@repo/shared-types"

interface BackgroundTask {
  id: string
  type: string
  status: string
  title: string
  description?: string
  parameters: any
  result?: any
  errorMessage?: string
  retryCount: number
  maxRetries: number
  createdAt: string
  startedAt?: string
  completedAt?: string
  user: {
    id: string
    name: string
    email: string
  }
}

interface TaskStats {
  total: number
  pending: number
  processing: number
  success: number
  failed: number
  failedQueued: number
}

export default function BackgroundTasksPage() {
  const params = useParams()
  const projectId = params.project_id as string

  const [tasks, setTasks] = useState<BackgroundTask[]>([])
  const [stats, setStats] = useState<TaskStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")

  // 获取任务列表
  const fetchTasks = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        projectId,
        page: page.toString(),
        limit: "20"
      })

      if (statusFilter && statusFilter !== "all") params.append("status", statusFilter)
      if (typeFilter && typeFilter !== "all") params.append("type", typeFilter)

      const response = await fetchGet(`/api/background-tasks?${params}`)
      setTasks(response.data)
      setTotalPages(response.pagination.totalPages)
      setCurrentPage(page)
    } catch (error) {
      console.error("获取任务列表失败:", error)
      toast.error("获取任务列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await fetchGet(`/api/background-tasks/stats/${projectId}`)
      setStats(response)
    } catch (error) {
      console.error("获取统计信息失败:", error)
    }
  }

  // 重试任务
  const retryTask = async (taskId: string) => {
    try {
      await fetchPost(`/api/background-tasks/${taskId}/retry`, {})
      toast.success("任务已重新加入队列")
      fetchTasks(currentPage)
      fetchStats()
    } catch (error) {
      console.error("重试任务失败:", error)
      toast.error("重试任务失败")
    }
  }

  // 删除任务
  const deleteTask = async (taskId: string) => {
    try {
      await fetchDelete(`/api/background-tasks/${taskId}`)
      toast.success("任务已删除")
      fetchTasks(currentPage)
      fetchStats()
    } catch (error) {
      console.error("删除任务失败:", error)
      toast.error("删除任务失败")
    }
  }

  // 批量删除任务
  const batchDeleteTasks = async () => {
    if (selectedTasks.length === 0) {
      toast.error("请选择要删除的任务")
      return
    }

    try {
      const response = await fetchPost("/api/background-tasks/batch-delete", {
        taskIds: selectedTasks,
        projectId
      })
      toast.success(response.message)
      setSelectedTasks([])
      fetchTasks(currentPage)
      fetchStats()
    } catch (error) {
      console.error("批量删除失败:", error)
      toast.error("批量删除失败")
    }
  }

  // 获取状态显示
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      [BACKGROUND_TASK_STATUS.PENDING]: { label: "待处理", variant: "secondary" as const },
      [BACKGROUND_TASK_STATUS.PROCESSING]: { label: "处理中", variant: "default" as const },
      [BACKGROUND_TASK_STATUS.SUCCESS]: { label: "成功", variant: "success" as const },
      [BACKGROUND_TASK_STATUS.FAILED]: { label: "失败", variant: "destructive" as const },
      [BACKGROUND_TASK_STATUS.FAILED_QUEUED]: { label: "失败重试", variant: "warning" as const },
    }

    // 使用类型断言确保 TypeScript 理解这是有效的键
    const config = statusConfig[status as BackgroundTaskStatus] || { label: status, variant: "secondary" as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // 获取类型显示
  const getTypeLabel = (type: string) => {
    const typeLabels = {
      [BACKGROUND_TASK_TYPE.TRANSLATION]: "翻译任务"
    } as Record<string, string>
    return typeLabels[type] || type
  }

  // 格式化时间
  const formatTime = (timeString?: string) => {
    if (!timeString) return "-"
    return new Date(timeString).toLocaleString("zh-CN")
  }

  // 计算耗时
  const getDuration = (startedAt?: string, completedAt?: string) => {
    if (!startedAt) return "-"
    const start = new Date(startedAt).getTime()
    const end = completedAt ? new Date(completedAt).getTime() : Date.now()
    const duration = Math.round((end - start) / 1000)
    return `${duration}秒`
  }

  useEffect(() => {
    fetchTasks()
    fetchStats()
  }, [projectId, statusFilter, typeFilter])

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">后台任务管理</h1>
        <p className="text-muted-foreground">查看和管理项目的后台任务</p>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-muted-foreground">总任务</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-sm text-muted-foreground">待处理</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
              <div className="text-sm text-muted-foreground">处理中</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.success}</div>
              <div className="text-sm text-muted-foreground">成功</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
              <div className="text-sm text-muted-foreground">失败</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.failedQueued}</div>
              <div className="text-sm text-muted-foreground">重试中</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 操作栏 */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>任务列表</CardTitle>
            <div className="flex items-center gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value={BACKGROUND_TASK_STATUS.PENDING}>待处理</SelectItem>
                  <SelectItem value={BACKGROUND_TASK_STATUS.PROCESSING}>处理中</SelectItem>
                  <SelectItem value={BACKGROUND_TASK_STATUS.SUCCESS}>成功</SelectItem>
                  <SelectItem value={BACKGROUND_TASK_STATUS.FAILED}>失败</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="类型筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value={BACKGROUND_TASK_TYPE.TRANSLATION}>翻译任务</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchTasks(currentPage)}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                刷新
              </Button>

              {selectedTasks.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={batchDeleteTasks}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  删除选中 ({selectedTasks.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : tasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">暂无任务</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedTasks.length === tasks.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedTasks(tasks.map(t => t.id))
                        } else {
                          setSelectedTasks([])
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>任务</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>耗时</TableHead>
                  <TableHead>重试</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedTasks.includes(task.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedTasks([...selectedTasks, task.id])
                          } else {
                            setSelectedTasks(selectedTasks.filter(id => id !== task.id))
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{task.title}</div>
                        {task.description && (
                          <div className="text-sm text-muted-foreground">{task.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getTypeLabel(task.type)}</TableCell>
                    <TableCell>{getStatusBadge(task.status)}</TableCell>
                    <TableCell>{formatTime(task.createdAt)}</TableCell>
                    <TableCell>{getDuration(task.startedAt, task.completedAt)}</TableCell>
                    <TableCell>{task.retryCount}/{task.maxRetries}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>任务详情</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <strong>任务ID:</strong> {task.id}
                              </div>
                              <div>
                                <strong>参数:</strong>
                                <pre className="mt-1 p-2 bg-muted rounded text-sm">
                                  {JSON.stringify(task.parameters, null, 2)}
                                </pre>
                              </div>
                              {task.result && (
                                <div>
                                  <strong>结果:</strong>
                                  <pre className="mt-1 p-2 bg-muted rounded text-sm">
                                    {JSON.stringify(task.result, null, 2)}
                                  </pre>
                                </div>
                              )}
                              {task.errorMessage && (
                                <div>
                                  <strong>错误信息:</strong>
                                  <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                                    {task.errorMessage}
                                  </div>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>

                        {task.status === BACKGROUND_TASK_STATUS.FAILED && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => retryTask(task.id)}
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        )}

                        {[BACKGROUND_TASK_STATUS.SUCCESS, BACKGROUND_TASK_STATUS.FAILED].includes(task.status as any) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteTask(task.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => fetchTasks(currentPage - 1)}
              >
                上一页
              </Button>
              <span className="text-sm">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => fetchTasks(currentPage + 1)}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
