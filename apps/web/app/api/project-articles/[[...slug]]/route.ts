import { prisma } from "@repo/db"
import { R } from "@repo/utils/server"
import { authUser, hasProjectAccess } from "@repo/auth/server"
import { AutoRouter } from "itty-router"
import { z } from "zod"

const router = AutoRouter({ base: "/api/project-articles" })

// 文章创建参数验证
const createArticleSchema = z.object({
	projectId: z.string(),
	slug: z.string().min(1, "文章访问路径不能为空"),
	title: z.string().min(1, "文章标题不能为空"),
	mdxContent: z.string(),
	locale: z.string(),
	titleImageUrl: z.string().optional().nullable(),
	author: z.string().optional().nullable(),
	authorImageUrl: z.string().optional().nullable(),
	readTime: z.string().optional().nullable(),
	tags: z.array(z.string()).optional().default([]),
	categoryCode: z.string().optional().nullable(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional().default("DRAFT"),
	metadata: z.object({
		title: z.string(),
		description: z.string(),
		ogTitle: z.string().optional().nullable(),
		ogDescription: z.string().optional().nullable(),
		ogImage: z.string().optional().nullable(),
		ogUrl: z.string().optional().nullable(),
		twitterCard: z.string().optional().nullable(),
		twitterTitle: z.string().optional().nullable(),
		twitterDescription: z.string().optional().nullable(),
		twitterImage: z.string().optional().nullable(),
		structuredData: z.record(z.any()).optional().nullable(),
	}),
})

// 文章更新参数验证
const updateArticleSchema = createArticleSchema.partial().extend({
	id: z.string(),
})

// 获取文章列表
router.get("/page", async (request) => {
	try {
		const {
			projectId,
			search,
			categoryCode,
			tag,
			status,
			locale,
			page = 1,
			pageSize = 10,
		} = request.query

		// 验证项目访问权限
		const auth = await hasProjectAccess(projectId as string)

		const take = Math.min(Number(pageSize), 20)
		const skip = (Number(page) - 1) * take

		// 构建查询条件
		const where: any = {
			projectId: projectId as string,
			userId: auth.id,
			...(search && { title: { contains: search as string } }),
			...(status && { status: status as string }),
			...(locale && { locale: locale as string }),
			...(categoryCode && { categoryCode: categoryCode as string }),
		}

		// 根据标签筛选
		if (tag) {
			where.tags = {
				array_contains: [tag as string],
			}
		}

		// 查询文章列表
		const [articles, total] = await Promise.all([
			prisma.projectArticle.findMany({
				where,
				take,
				skip,
				orderBy: { updatedAt: "desc" },
				select: {
					id: true,
					slug: true,
					title: true,
					locale: true,
					titleImageUrl: true,
					author: true,
					authorImageUrl: true,
					readTime: true,
					tags: true,
					categoryCode: true,
					status: true,
					createdAt: true,
					updatedAt: true,
				},
			}),
			prisma.projectArticle.count({ where }),
		])

		// 转换数据格式，将 updatedAt 映射为 updateTime
		const formattedArticles = articles.map(article => ({
			...article,
			updateTime: article.updatedAt.toISOString(),
		}))

		return R.ok({
			data: formattedArticles,
			pagination: {
				page: Number(page),
				pageSize: take,
				total,
				totalPages: Math.ceil(total / take),
			},
		})
	} catch (error) {
		console.error("获取文章列表失败:", error)
		return R.error("获取文章列表失败")
	}
})

// 获取单个文章
router.get("/", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const { id } = request.query

		// 获取文章详情
		const article = await prisma.projectArticle.findUnique({
			where: { id: id as string },
		})

		if (!article) {
			return R.notFound("文章不存在")
		}

		// 检查权限
		if (article.userId !== auth.id) {
			return R.raw({ code: 403, message: "无权访问该文章" }, { status: 403 })
		}

		// 转换数据格式，将 updatedAt 映射为 updateTime
		const formattedArticle = {
			...article,
			updateTime: article.updatedAt.toISOString(),
		}

		return R.ok(formattedArticle)
	} catch (error) {
		console.error("获取文章详情失败:", error)
		return R.error("获取文章详情失败")
	}
})

// 创建文章
router.post("/", async (request) => {
	try {
		const body = await request.json()
		const data = createArticleSchema.parse(body)
		const { projectId } = data

		// 验证项目访问权限
		const auth = await hasProjectAccess(projectId)

		// 检查slug是否已存在
		const existingArticle = await prisma.projectArticle.findFirst({
			where: {
				projectId: projectId,
				slug: data.slug,
				locale: data.locale,
			},
		})

		if (existingArticle) {
			return R.bad("文章访问路径在该语言下已存在")
		}

		// 创建文章
		const article = await prisma.projectArticle.create({
			data: {
				...data,
				userId: auth.id,
				titleImageUrl: data.titleImageUrl || "",
				author: data.author || null,
				authorImageUrl: data.authorImageUrl || null,
				readTime: data.readTime || null,
				categoryCode: data.categoryCode || null,
			},
		})

		// 转换数据格式，将 updatedAt 映射为 updateTime
		const formattedArticle = {
			...article,
			updateTime: article.updatedAt.toISOString(),
		}

		return R.ok(formattedArticle)
	} catch (error) {
		console.error("创建文章失败:", error)
		return R.error("创建文章失败")
	}
})

// 更新文章
router.put("/", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const body = await request.json()
		const data = updateArticleSchema.parse(body)

		// 检查文章是否存在
		const existingArticle = await prisma.projectArticle.findUnique({
			where: { id: data.id },
		})

		if (!existingArticle) {
			return R.notFound("文章不存在")
		}

		// 检查权限
		if (existingArticle.userId !== auth.id) {
			return R.raw({ code: 403, message: "无权访问该文章" }, { status: 403 })
		}

		// 如果更新了slug，检查是否冲突
		if (data.slug && data.slug !== existingArticle.slug) {
			const conflictArticle = await prisma.projectArticle.findFirst({
				where: {
					projectId: existingArticle.projectId,
					slug: data.slug,
					locale: data.locale || existingArticle.locale,
					id: { not: data.id },
				},
			})

			if (conflictArticle) {
				return R.bad("文章访问路径在该语言下已存在")
			}
		}

		// 更新文章
		const updatedArticle = await prisma.projectArticle.update({
			where: { id: data.id },
			data: {
				...data,
				id: undefined, // 移除id字段
			},
		})

		// 转换数据格式，将 updatedAt 映射为 updateTime
		const formattedArticle = {
			...updatedArticle,
			updateTime: updatedArticle.updatedAt.toISOString(),
		}

		return R.ok(formattedArticle)
	} catch (error) {
		console.error("更新文章失败:", error)
		return R.error("更新文章失败")
	}
})

// 删除文章
router.delete("/", async (request) => {
	try {
		const auth = await authUser()
		if (!auth)
			return R.raw({ code: 401, message: "未授权访问" }, { status: 401 })

		const { id } = request.query

		if (!id) {
			return R.badRequest("缺少文章ID")
		}

		// 检查文章是否存在
		const existingArticle = await prisma.projectArticle.findUnique({
			where: { id: id as string },
		})

		if (!existingArticle) {
			return R.notFound("文章不存在")
		}

		// 检查权限
		if (existingArticle.userId !== auth.id) {
			return R.raw({ code: 403, message: "无权访问该文章" }, { status: 403 })
		}

		// 删除文章
		await prisma.projectArticle.delete({
			where: { id: id as string },
		})

		return R.ok({ success: true, message: "文章删除成功" })
	} catch (error) {
		console.error("删除文章失败:", error)
		return R.error("删除文章失败")
	}
})

// 翻译文章
router.post("/translate", async (request) => {
	try {
		const auth = await authUser()
		if (!auth) {
			return R.unauthorized()
		}

		const body = await request.json()
		const { id, sourceLocale, targetLocales } = body

		if (
			!id ||
			!sourceLocale ||
			!targetLocales ||
			!Array.isArray(targetLocales)
		) {
			return R.badRequest("缺少必要参数")
		}

		// 检查源文章是否存在
		const sourceArticle = await prisma.projectArticle.findUnique({
			where: { id: id as string },
		})

		if (!sourceArticle) {
			return R.notFound("源文章不存在")
		}

		// 检查权限
		if (sourceArticle.userId !== auth.id) {
			return R.raw({ code: 403, message: "无权访问该文章" }, { status: 403 })
		}

		// TODO: 实现文章翻译逻辑
		// 这里保留翻译接口，后续可以集成翻译服务

		return R.ok({
			success: true,
			message: "文章翻译功能暂未实现",
			results: {
				success: [],
				failed: targetLocales.map((locale) => ({ locale, error: "翻译功能暂未实现" })),
			},
		})
	} catch (error) {
		console.error("翻译文章失败:", error)
		return R.error("翻译文章失败")
	}
})

export const GET = async (request: Request) => router.fetch(request)
export const POST = async (request: Request) => router.fetch(request)
export const PUT = async (request: Request) => router.fetch(request)
export const DELETE = async (request: Request) => router.fetch(request)
