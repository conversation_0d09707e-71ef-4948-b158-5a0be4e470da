
import { prisma } from "@repo/db"
import { hasProjectAccess, authUser } from "@repo/auth/server"
import { R } from "@repo/utils/server"
import { getLogger } from "@repo/logger"
import { z } from "zod"
import {
  PROMPT_TEMPLATE_TYPE,
  PROMPT_TEST_STATUS,
  DAILY_TEST_LIMIT,
  type PromptTemplateType
} from "@repo/shared-types"
import { AutoRouter } from "itty-router"

const router = AutoRouter({
  base: "/api/prompts",
})

const logger = getLogger("PromptAPI")

// 创建/更新提示词的验证模式
const promptTemplateSchema = z.object({
  id: z.string().optional(),
  projectId: z.string(),
  type: z.enum(Object.values(PROMPT_TEMPLATE_TYPE) as [PromptTemplateType, ...PromptTemplateType[]]),
  name: z.string().min(1, "提示词名称不能为空").max(200, "提示词名称不能超过200字符"),
  description: z.string().optional(),
  content: z.string().min(1, "提示词内容不能为空"),
  isEnabled: z.boolean().default(true),
})

// 提示词测试的验证模式
const promptTestSchema = z.object({
  promptTemplateId: z.string(),
  projectId: z.string(),
  inputParams: z.record(z.any()),
})

// 提示词导入的验证模式
const promptImportSchema = z.object({
  sourceProjectId: z.string(),
  targetProjectId: z.string(),
})

// 获取项目的提示词列表（包含系统级提示词作为默认值）
router.get("/", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const url = new URL(request.url)
    const projectId = url.searchParams.get("projectId")
    const type = url.searchParams.get("type")
    const page = parseInt(url.searchParams.get("page") || "1")
    const limit = parseInt(url.searchParams.get("limit") || "20")
    const includeSystemDefaults = url.searchParams.get("includeSystemDefaults") === "true"

    if (!projectId) {
      return R.badRequest("缺少项目ID")
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 构建查询条件
    const where: any = { projectId }
    if (type) where.type = type

    const skip = (page - 1) * limit

    // 查询项目级提示词列表和总数
    const [projectPrompts, projectTotal] = await Promise.all([
      prisma.promptTemplate.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.promptTemplate.count({ where })
    ])

    let allPrompts = projectPrompts
    let total = projectTotal

    // 如果需要包含系统默认提示词
    if (includeSystemDefaults) {
      // 获取所有系统级提示词
      const systemWhere: any = { projectId: null, isDefault: true }
      if (type) systemWhere.type = type

      const systemPrompts = await prisma.promptTemplate.findMany({
        where: systemWhere,
        orderBy: { type: "asc" }
      })

      // 获取项目已有的提示词类型
      const existingTypes = new Set(projectPrompts.map(p => p.type))

      // 添加项目中不存在的系统级提示词，标记为系统默认
      const missingSystemPrompts = systemPrompts
        .filter(sp => !existingTypes.has(sp.type))
        .map(sp => ({
          ...sp,
          isSystemDefault: true, // 标记为系统默认
          creator: {
            id: 'system',
            name: '系统',
            email: '<EMAIL>'
          }
        }))

      allPrompts = [...projectPrompts, ...missingSystemPrompts]
      total = projectTotal + missingSystemPrompts.length
    }

    return R.ok({
      data: allPrompts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    logger.error("获取提示词列表失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 获取单个提示词详情
router.get("/detail", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const url = new URL(request.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    const prompt = await prisma.promptTemplate.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!prompt) {
      return R.notFound("提示词不存在")
    }

    // 验证项目访问权限
    if (prompt.projectId) {
      await hasProjectAccess(prompt.projectId)
    }

    return R.ok(prompt)
  } catch (error) {
    logger.error("获取提示词详情失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 创建新提示词
router.post("/create", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const validation = promptTemplateSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { projectId, type, name, description, content, isEnabled } = validation.data

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 检查同类型提示词是否已存在
    const existingPrompt = await prisma.promptTemplate.findFirst({
      where: {
        projectId,
        type
      }
    })

    if (existingPrompt) {
      return R.badRequest(`该项目已存在 ${type} 类型的提示词`)
    }

    const newPrompt = await prisma.promptTemplate.create({
      data: {
        projectId,
        type,
        name,
        description,
        content,
        isEnabled,
        createdBy: user.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return R.ok(newPrompt)
  } catch (error) {
    logger.error("创建提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 更新提示词
router.post("/update", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const validation = promptTemplateSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { id, projectId, name, description, content, isEnabled } = validation.data

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 检查提示词是否存在
    const existingPrompt = await prisma.promptTemplate.findUnique({
      where: { id }
    })

    if (!existingPrompt) {
      return R.notFound("提示词不存在")
    }

    if (existingPrompt.projectId !== projectId) {
      return R.forbidden("无权限修改此提示词")
    }

    const updatedPrompt = await prisma.promptTemplate.update({
      where: { id },
      data: {
        name,
        description,
        content,
        isEnabled,
        updatedAt: new Date()
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return R.ok(updatedPrompt)
  } catch (error) {
    logger.error("更新提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 删除提示词
router.post("/delete", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const { id } = body

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    const prompt = await prisma.promptTemplate.findUnique({
      where: { id }
    })

    if (!prompt) {
      return R.notFound("提示词不存在")
    }

    // 验证项目访问权限
    if (prompt.projectId) {
      await hasProjectAccess(prompt.projectId)
    }

    // 检查是否为系统默认提示词
    if (prompt.isDefault) {
      return R.badRequest("不能删除系统默认提示词")
    }

    await prisma.promptTemplate.delete({
      where: { id }
    })

    return R.ok({ message: "提示词删除成功" })
  } catch (error) {
    logger.error("删除提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 导入提示词
router.post("/import", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const validation = promptImportSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { sourceProjectId, targetProjectId } = validation.data

    // 验证两个项目的访问权限
    await hasProjectAccess(sourceProjectId)
    await hasProjectAccess(targetProjectId)

    // 获取源项目的所有提示词
    const sourcePrompts = await prisma.promptTemplate.findMany({
      where: {
        projectId: sourceProjectId,
        isEnabled: true
      }
    })

    if (sourcePrompts.length === 0) {
      return R.badRequest("源项目没有可导入的提示词")
    }

    // 批量导入提示词（覆盖同类型的提示词）
    const importResults = []

    for (const sourcePrompt of sourcePrompts) {
      try {
        // 检查目标项目是否已有同类型提示词
        const existingPrompt = await prisma.promptTemplate.findFirst({
          where: {
            projectId: targetProjectId,
            type: sourcePrompt.type
          }
        })

        if (existingPrompt) {
          // 更新现有提示词
          const updatedPrompt = await prisma.promptTemplate.update({
            where: { id: existingPrompt.id },
            data: {
              name: sourcePrompt.name,
              description: sourcePrompt.description,
              content: sourcePrompt.content,
              isEnabled: sourcePrompt.isEnabled,
              updatedAt: new Date()
            }
          })
          importResults.push({ type: sourcePrompt.type, action: 'updated', id: updatedPrompt.id })
        } else {
          // 创建新提示词
          const newPrompt = await prisma.promptTemplate.create({
            data: {
              projectId: targetProjectId,
              type: sourcePrompt.type,
              name: sourcePrompt.name,
              description: sourcePrompt.description,
              content: sourcePrompt.content,
              isEnabled: sourcePrompt.isEnabled,
              createdBy: user.id
            }
          })
          importResults.push({ type: sourcePrompt.type, action: 'created', id: newPrompt.id })
        }
      } catch (error) {
        logger.error(`导入提示词失败 ${sourcePrompt.type}:`, error)
        const errorMessage = error instanceof Error ? error.message : String(error)
        importResults.push({ type: sourcePrompt.type, action: 'failed', error: errorMessage })
      }
    }

    return R.ok({
      message: "提示词导入完成",
      results: importResults,
      summary: {
        total: sourcePrompts.length,
        success: importResults.filter(r => r.action !== 'failed').length,
        failed: importResults.filter(r => r.action === 'failed').length
      }
    })
  } catch (error) {
    logger.error("导入提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 测试提示词
router.post("/test", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const validation = promptTestSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { promptTemplateId, projectId, inputParams } = validation.data

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 检查今日测试次数限制
    const today = new Date().toISOString().split('T')[0] as string
    const usageLimit = await prisma.projectPromptUsageLimit.findUnique({
      where: {
        project_date_unique: {
          projectId,
          date: today
        }
      }
    })

    if (usageLimit && usageLimit.usedTestCount >= usageLimit.maxTestCount) {
      return R.badRequest(`今日测试次数已达上限 (${usageLimit.maxTestCount}次)`)
    }

    // 获取提示词模板
    const promptTemplate = await prisma.promptTemplate.findUnique({
      where: { id: promptTemplateId }
    })

    if (!promptTemplate) {
      return R.notFound("提示词模板不存在")
    }

    if (!promptTemplate.isEnabled) {
      return R.badRequest("提示词模板已禁用")
    }

    // 创建测试记录
    const testRecord = await prisma.promptTestRecord.create({
      data: {
        promptTemplateId,
        projectId,
        userId: user.id,
        inputParams,
        status: PROMPT_TEST_STATUS.PENDING
      }
    })

    const startTime = Date.now()

    try {
      // 更新测试状态为处理中
      await prisma.promptTestRecord.update({
        where: { id: testRecord.id },
        data: { status: PROMPT_TEST_STATUS.PROCESSING }
      })

      // TODO: 这里应该调用实际的AI服务进行测试
      // 现在先返回模拟结果
      const mockResult = {
        success: true,
        result: "这是一个模拟的AI响应结果",
        model: "mock-model",
        tokens: {
          input: 100,
          output: 50,
          total: 150
        }
      }

      const duration = Date.now() - startTime

      // 更新测试记录
      await prisma.promptTestRecord.update({
        where: { id: testRecord.id },
        data: {
          status: PROMPT_TEST_STATUS.SUCCESS,
          outputResult: mockResult,
          duration
        }
      })

      // 更新使用次数
      await prisma.projectPromptUsageLimit.upsert({
        where: {
          project_date_unique: {
            projectId,
            date: today
          }
        },
        update: {
          usedTestCount: {
            increment: 1
          }
        },
        create: {
          projectId,
          date: today,
          usedTestCount: 1,
          maxTestCount: DAILY_TEST_LIMIT
        }
      })

      return R.ok({
        testId: testRecord.id,
        result: mockResult,
        duration,
        remainingTests: (usageLimit?.maxTestCount || DAILY_TEST_LIMIT) - (usageLimit?.usedTestCount || 0) - 1
      })
    } catch (error) {
      const duration = Date.now() - startTime

      // 更新测试记录为失败
      const errorMessage = error instanceof Error ? error.message : String(error)
      await prisma.promptTestRecord.update({
        where: { id: testRecord.id },
        data: {
          status: PROMPT_TEST_STATUS.FAILED,
          errorMessage,
          duration
        }
      })

      throw error
    }
  } catch (error) {
    logger.error("测试提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 基于系统提示词创建项目提示词
router.post("/create-from-system", async (request) => {
  try {
    const user = await authUser()
    if (!user) {
      return R.unauthorized()
    }

    const body = await request.json()
    const { systemPromptId, projectId } = body

    if (!systemPromptId || !projectId) {
      return R.badRequest("缺少系统提示词ID或项目ID")
    }

    // 验证项目访问权限
    await hasProjectAccess(projectId)

    // 获取系统提示词
    const systemPrompt = await prisma.promptTemplate.findUnique({
      where: { id: systemPromptId }
    })

    if (!systemPrompt) {
      return R.notFound("系统提示词不存在")
    }

    // 确保是系统级提示词
    if (systemPrompt.projectId !== null) {
      return R.badRequest("不是系统级提示词")
    }

    // 检查项目中是否已存在同类型提示词
    const existingPrompt = await prisma.promptTemplate.findFirst({
      where: {
        projectId,
        type: systemPrompt.type
      }
    })

    if (existingPrompt) {
      return R.badRequest(`该项目已存在 ${systemPrompt.type} 类型的提示词`)
    }

    // 基于系统提示词创建项目提示词
    const newPrompt = await prisma.promptTemplate.create({
      data: {
        projectId,
        type: systemPrompt.type,
        name: systemPrompt.name.replace('系统默认 - ', ''),
        description: systemPrompt.description,
        content: systemPrompt.content,
        isEnabled: true,
        isDefault: false,
        createdBy: user.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return R.ok(newPrompt)
  } catch (error) {
    logger.error("基于系统提示词创建项目提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

export const GET = async (request: Request) => router.fetch(request)
export const POST = async (request: Request) => router.fetch(request)


