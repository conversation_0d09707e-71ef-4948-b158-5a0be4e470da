import { authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { UserRole } from "@lib/consts"
import { R } from "@repo/utils/server"
import { AutoRouter } from "itty-router"
import { z } from "zod"
import { NextRequest } from "next/server"
import { offsetPage } from "@repo/utils"
import { getLogger } from "@repo/logger"

// 创建模块专用日志记录器
const logger = getLogger("products-management")

// 创建路由实例
const router = AutoRouter({ base: "/api/products-management" })

// 验证产品数据的schema
const productSchema = z.object({
	code: z.string().min(1, "产品代码不能为空"),
	name: z.string().min(1, "产品名称不能为空"),
	description: z.string().min(1, "产品描述不能为空"),
	type: z.string().min(1, "产品类型不能为空"),
	price: z.number().int().min(0, "价格不能为负数"),
	currency: z.string().min(1, "币种不能为空"),
	credits: z.number().int().min(0, "信用点不能为负数"),
	siteCount: z.number().int().min(0, "站点数量不能为负数"),
	discount: z.number().min(0).max(1, "折扣必须在0-1之间"),
	discountPrice: z.number().int().min(0, "折扣价不能为负数"),
	isPopular: z.boolean().default(false),
	sortOrder: z.number().int().default(0),
	status: z.string().min(1, "状态不能为空"),
	locale: z.string().min(1, "语言不能为空"),
	features: z.array(z.string()).default([]),
	metadata: z.any().optional(),
	benefitRules: z
		.array(
			z.object({
				benefitCode: z.string(),
				maxQuantity: z.number().int(),
				validDays: z.number().int(),
			}),
		)
		.optional(),
})

// 验证查询参数的schema
const querySchema = z.object({
	page: z.string().transform(Number).default("1"),
	pageSize: z.string().transform(Number).default("10"),
	search: z.string().nullable().optional(),
	type: z.string().nullable().optional(),
	code: z.string().nullable().optional(),
	name: z.string().nullable().optional(),
})

// 获取产品列表
router.get("/", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		// 解析查询参数
		const searchParams = request.nextUrl.searchParams
		const { page, pageSize, search, type, code, name } = querySchema.parse({
			page: searchParams.get("page"),
			pageSize: searchParams.get("pageSize"),
			search: searchParams.get("search"),
			type: searchParams.get("type"),
			code: searchParams.get("code"),
			name: searchParams.get("name"),
		})

		// 构建查询条件
		const where: any = {}
		const conditions: any[] = []

		if (search) {
			conditions.push({
				OR: [
					{ code: { contains: search, mode: "insensitive" } },
					{ name: { contains: search, mode: "insensitive" } },
					{ description: { contains: search, mode: "insensitive" } },
				],
			})
		}

		if (code) {
			conditions.push({ code: { contains: code, mode: "insensitive" } })
		}

		if (name) {
			conditions.push({ name: { contains: name, mode: "insensitive" } })
		}

		if (type) {
			conditions.push({ type })
		}

		// 合并所有条件
		if (conditions.length > 0) {
			where.AND = conditions
		}

		// 计算分页
		const { skip, take } = offsetPage(page, pageSize)

		// 查询数据
		const [products, total] = await Promise.all([
			prisma.product.findMany({
				where,
				orderBy: [{ updatedAt: "desc" }, { code: "asc" }],
				skip,
				take,
			}),
			prisma.product.count({ where }),
		])

		return R.ok({ data: products, total })
	} catch (error) {
		logger.error("获取产品列表失败:", error)
		return R.error("获取产品列表失败")
	}
})

// 获取单个产品
router.get("/detail", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const searchParams = request.nextUrl.searchParams
		const id = searchParams.get("id")
		if (!id) {
			return R.error("产品ID不能为空")
		}

		const productId = parseInt(id)
		if (Number.isNaN(productId)) {
			return R.error("无效的产品ID")
		}

		const product = await prisma.product.findUnique({
			where: { id: productId },
			include: {
				benefitRules: true,
			},
		})

		if (!product) {
			return R.notFound("产品不存在")
		}

		return R.ok(product)
	} catch (error) {
		logger.error("获取产品详情失败:", error)
		return R.error("获取产品详情失败")
	}
})

// 获取产品权益规则
router.get("/benefit-rules", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const searchParams = request.nextUrl.searchParams
		const id = searchParams.get("id")
		if (!id) {
			return R.error("产品ID不能为空")
		}

		const productId = parseInt(id)
		if (Number.isNaN(productId)) {
			return R.error("无效的产品ID")
		}

		const product = await prisma.product.findUnique({
			where: { id: productId },
			include: {
				benefitRules: true,
			},
		})

		if (!product) {
			return R.notFound("产品不存在")
		}

		return R.ok({ data: product.benefitRules })
	} catch (error) {
		logger.error("获取产品权益规则失败:", error)
		return R.error("获取产品权益规则失败")
	}
})

// 创建产品
router.post("/", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const body = await request.json()
		const validatedData = productSchema.parse(body)

		// 提取benefitRules
		const { benefitRules, ...productData } = validatedData

		// 确保features字段有值
		if (!productData.features) {
			productData.features = []
		}

		// 检查代码是否已存在
		const existingProduct = await prisma.product.findUnique({
			where: { code: productData.code },
		})

		if (existingProduct) {
			return R.error("产品代码已存在")
		}

		// 创建产品和权益规则
		const product = await prisma.$transaction(async (tx) => {
			// 创建产品
			const newProduct = await tx.product.create({
				data: productData,
			})

			// 创建权益规则
			if (benefitRules && benefitRules.length > 0) {
				for (const rule of benefitRules) {
					await tx.productBenefitRule.create({
						data: {
							productCode: newProduct.code,
							benefitCode: rule.benefitCode,
							maxQuantity: rule.maxQuantity,
							validDays: rule.validDays,
						},
					})
				}
			}

			return newProduct
		})

		return R.ok(product)
	} catch (error) {
		if (error instanceof z.ZodError) {
			return R.error(
				`数据验证失败: ${error.errors.map((e) => e.message).join(", ")}`,
			)
		}

		logger.error("创建产品失败:", error)
		return R.error("创建产品失败")
	}
})

// 更新产品
router.put("/update", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const searchParams = request.nextUrl.searchParams
		const id = searchParams.get("id")
		if (!id) {
			return R.error("产品ID不能为空")
		}

		const productId = parseInt(id)
		if (Number.isNaN(productId)) {
			return R.error("无效的产品ID")
		}

		const body = await request.json()
		const validatedData = productSchema.parse(body)

		// 提取benefitRules
		const { benefitRules, ...productData } = validatedData

		// 确保features字段有值
		if (!productData.features) {
			productData.features = []
		}

		// 检查产品是否存在
		const existingProduct = await prisma.product.findUnique({
			where: { id: productId },
		})

		if (!existingProduct) {
			return R.notFound("产品不存在")
		}

		// 更新产品和权益规则
		const product = await prisma.$transaction(async (tx) => {
			// 更新产品
			const updatedProduct = await tx.product.update({
				where: { id: productId },
				data: {
					name: productData.name,
					description: productData.description,
					type: productData.type,
					price: productData.price,
					currency: productData.currency,
					credits: productData.credits,
					siteCount: productData.siteCount,
					discount: productData.discount,
					discountPrice: productData.discountPrice,
					isPopular: productData.isPopular,
					sortOrder: productData.sortOrder,
					status: productData.status,
					locale: productData.locale,
					features: productData.features,
					metadata: productData.metadata,
				},
			})

			// 删除现有权益规则
			await tx.productBenefitRule.deleteMany({
				where: { productCode: updatedProduct.code },
			})

			// 创建新的权益规则
			if (benefitRules && benefitRules.length > 0) {
				for (const rule of benefitRules) {
					await tx.productBenefitRule.create({
						data: {
							productCode: updatedProduct.code,
							benefitCode: rule.benefitCode,
							maxQuantity: rule.maxQuantity,
							validDays: rule.validDays,
						},
					})
				}
			}

			return updatedProduct
		})

		return R.ok(product)
	} catch (error) {
		if (error instanceof z.ZodError) {
			return R.error(
				`数据验证失败: ${error.errors.map((e) => e.message).join(", ")}`,
			)
		}

		logger.error("更新产品失败:", error)
		return R.error("更新产品失败")
	}
})

// 删除产品
router.delete("/delete", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const searchParams = request.nextUrl.searchParams
		const id = searchParams.get("id")
		if (!id) {
			return R.error("产品ID不能为空")
		}

		const productId = parseInt(id)
		if (Number.isNaN(productId)) {
			return R.error("无效的产品ID")
		}

		// 检查产品是否存在
		const existingProduct = await prisma.product.findUnique({
			where: { id: productId },
			include: {
				userBenefits: true,
			},
		})

		if (!existingProduct) {
			return R.notFound("产品不存在")
		}

		// 检查是否有关联数据
		if (existingProduct.userBenefits.length > 0) {
			return R.error("该产品已被用户使用，无法删除")
		}

		// 删除产品和权益规则
		await prisma.$transaction(async (tx) => {
			// 删除权益规则
			await tx.productBenefitRule.deleteMany({
				where: { productCode: existingProduct.code },
			})

			// 删除产品
			await tx.product.delete({
				where: { id: productId },
			})
		})

		return R.ok({ success: true })
	} catch (error) {
		logger.error("删除产品失败:", error)
		return R.error("删除产品失败")
	}
})

export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const PUT = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
