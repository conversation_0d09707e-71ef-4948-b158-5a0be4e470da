import { prisma } from "@repo/db"
import { R } from "@repo/utils/server"
import { getLogger } from "@repo/logger"
import { z } from "zod"
import {
  PROMPT_TEMPLATE_TYPE,
  DEFAULT_PROMPT_CONTENTS,
  type PromptTemplateType
} from "@repo/shared-types"
import { AutoRouter } from "itty-router"

const logger = getLogger("SystemPromptAPI")

// 系统提示词验证模式
const systemPromptSchema = z.object({
  id: z.string().optional(),
  type: z.enum(Object.values(PROMPT_TEMPLATE_TYPE) as [PromptTemplateType, ...PromptTemplateType[]]),
  name: z.string().min(1, "提示词名称不能为空").max(200, "提示词名称不能超过200字符"),
  description: z.string().optional(),
  content: z.string().min(1, "提示词内容不能为空"),
  isEnabled: z.boolean().default(true),
})

const router = AutoRouter({ base: "/api/system-prompts" })

// 获取系统提示词列表
router.get("/", async (request) => {
  try {
    const url = new URL(request.url)
    const type = url.searchParams.get("type")
    const page = parseInt(url.searchParams.get("page") || "1")
    const limit = parseInt(url.searchParams.get("limit") || "20")

    // 构建查询条件 - 只查询系统级提示词
    const where: any = { projectId: null }
    if (type) where.type = type

    const skip = (page - 1) * limit

    // 查询系统提示词列表和总数
    const [prompts, total] = await Promise.all([
      prisma.promptTemplate.findMany({
        where,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.promptTemplate.count({ where })
    ])

    return R.ok({
      data: prompts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    logger.error("获取系统提示词列表失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 获取单个系统提示词详情
router.get("/detail", async (request) => {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    const prompt = await prisma.promptTemplate.findUnique({
      where: { id }
    })

    if (!prompt) {
      return R.notFound("系统提示词不存在")
    }

    // 确保是系统级提示词
    if (prompt.projectId !== null) {
      return R.forbidden("不是系统级提示词")
    }

    return R.ok(prompt)
  } catch (error) {
    logger.error("获取系统提示词详情失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 创建新系统提示词
router.post("/create", async (request) => {
  try {
    const body = await request.json()
    const validation = systemPromptSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { type, name, description, content, isEnabled } = validation.data

    // 检查同类型系统提示词是否已存在
    const existingPrompt = await prisma.promptTemplate.findFirst({
      where: {
        projectId: null,
        type
      }
    })

    if (existingPrompt) {
      return R.badRequest(`系统已存在 ${type} 类型的提示词`)
    }

    const newPrompt = await prisma.promptTemplate.create({
      data: {
        projectId: null, // 系统级提示词
        type,
        name,
        description,
        content,
        isEnabled,
        isDefault: true, // 系统级提示词都是默认的
        createdBy: 'system' // 系统创建
      }
    })

    return R.ok(newPrompt)
  } catch (error) {
    logger.error("创建系统提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 更新系统提示词
router.post("/update", async (request) => {
  try {
    const body = await request.json()
    const validation = systemPromptSchema.safeParse(body)

    if (!validation.success) {
      return R.badRequest(validation.error.message)
    }

    const { id, name, description, content, isEnabled } = validation.data

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    // 检查系统提示词是否存在
    const existingPrompt = await prisma.promptTemplate.findUnique({
      where: { id }
    })

    if (!existingPrompt) {
      return R.notFound("系统提示词不存在")
    }

    // 确保是系统级提示词
    if (existingPrompt.projectId !== null) {
      return R.forbidden("不是系统级提示词")
    }

    const updatedPrompt = await prisma.promptTemplate.update({
      where: { id },
      data: {
        name,
        description,
        content,
        isEnabled,
        updatedAt: new Date()
      }
    })

    return R.ok(updatedPrompt)
  } catch (error) {
    logger.error("更新系统提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 删除系统提示词
router.post("/delete", async (request) => {
  try {
    const body = await request.json()
    const { id } = body

    if (!id) {
      return R.badRequest("缺少提示词ID")
    }

    const prompt = await prisma.promptTemplate.findUnique({
      where: { id }
    })

    if (!prompt) {
      return R.notFound("系统提示词不存在")
    }

    // 确保是系统级提示词
    if (prompt.projectId !== null) {
      return R.forbidden("不是系统级提示词")
    }

    await prisma.promptTemplate.delete({
      where: { id }
    })

    return R.ok({ message: "系统提示词删除成功" })
  } catch (error) {
    logger.error("删除系统提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

// 初始化系统默认提示词
router.post("/initialize", async (request) => {
  try {
    logger.info("开始初始化系统默认提示词...")

    const results = []

    for (const [type, content] of Object.entries(DEFAULT_PROMPT_CONTENTS)) {
      try {
        const existingPrompt = await prisma.promptTemplate.findFirst({
          where: {
            projectId: null,
            type,
            isDefault: true
          }
        })

        if (!existingPrompt) {
          const newPrompt = await prisma.promptTemplate.create({
            data: {
              projectId: null,
              type,
              name: `系统默认 - ${type}`,
              description: `系统默认的${type}提示词模板`,
              content,
              isEnabled: true,
              isDefault: true,
              createdBy: 'system'
            }
          })
          results.push({ type, action: 'created', id: newPrompt.id })
        } else {
          results.push({ type, action: 'exists', id: existingPrompt.id })
        }
      } catch (error) {
        logger.error(`初始化系统提示词失败 ${type}:`, error)
        const errorMessage = error instanceof Error ? error.message : String(error)
        results.push({ type, action: 'failed', error: errorMessage })
      }
    }

    return R.ok({
      message: "系统默认提示词初始化完成",
      results,
      summary: {
        total: Object.keys(DEFAULT_PROMPT_CONTENTS).length,
        created: results.filter(r => r.action === 'created').length,
        exists: results.filter(r => r.action === 'exists').length,
        failed: results.filter(r => r.action === 'failed').length
      }
    })
  } catch (error) {
    logger.error("初始化系统默认提示词失败:", error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    return R.error(errorMessage)
  }
})

export const GET = async (request: Request) => router.fetch(request)
export const POST = async (request: Request) => router.fetch(request)
