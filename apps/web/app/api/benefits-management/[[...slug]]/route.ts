import { authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { UserRole } from "@lib/consts"
import { R } from "@repo/utils/server"
import { AutoRouter } from "itty-router"
import { z } from "zod"
import { NextRequest } from "next/server"
import { offsetPage } from "@repo/utils"
import { getLogger } from "@repo/logger"

// 创建模块专用日志记录器
const logger = getLogger("benefits-management")

// 创建路由实例
const router = AutoRouter({ base: "/api/benefits-management" })

// 验证权益数据的schema
const benefitSchema = z.object({
	code: z.string().min(1, "权益代码不能为空"),
	name: z.string().min(1, "权益名称不能为空"),
	description: z.string().optional(),
	cost: z.number().int().min(0, "消耗点数不能为负数"),
	costRules: z.string().optional(),
})

// 验证查询参数的schema
const querySchema = z.object({
	page: z.string().nullable().transform(Number).default("1").optional(),
	pageSize: z.string().nullable().transform(Number).default("10").optional(),
	search: z.string().nullable().optional(),
	code: z.string().nullable().optional(),
	name: z.string().nullable().optional(),
	costRules: z.string().nullable().optional(),
})

// 获取权益列表
router.get("/", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		logger.warn(`非管理员用户 ${user?.id} 尝试访问权益列表`)
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		// 解析查询参数
		const searchParams = request.nextUrl.searchParams
		const {
			page = 1,
			pageSize = 10,
			search,
			code,
			name,
			costRules,
		} = querySchema.parse({
			page: searchParams.get("page"),
			pageSize: searchParams.get("pageSize"),
			search: searchParams.get("search"),
			code: searchParams.get("code"),
			name: searchParams.get("name"),
			costRules: searchParams.get("costRules"),
		})

		logger.info(
			`获取权益列表，页码: ${page}, 每页数量: ${pageSize}${search ? `, 搜索关键词: ${search}` : ""}`,
		)

		// 构建查询条件
		const where: any = {}
		const conditions: any[] = []

		// 处理搜索关键词
		if (search) {
			conditions.push({
				OR: [
					{ code: { contains: search, mode: "insensitive" } },
					{ name: { contains: search, mode: "insensitive" } },
					{ description: { contains: search, mode: "insensitive" } },
				],
			})
		}

		// 处理精确查询条件
		if (code) {
			conditions.push({ code: { contains: code, mode: "insensitive" } })
		}

		if (name) {
			conditions.push({ name: { contains: name, mode: "insensitive" } })
		}

		if (costRules) {
			conditions.push({ costRules })
		}

		// 合并所有查询条件
		if (conditions.length > 0) {
			where.AND = conditions
		}

		// 计算分页
		const { skip, take } = offsetPage(page, pageSize)
		logger.info(`计算分页: skip: ${skip}, take: ${take}`)
		// 查询数据
		const [benefits, total] = await Promise.all([
			prisma.benefit.findMany({
				where,
				orderBy: [{ updatedAt: "desc" }, { code: "asc" }],
				skip: skip ?? 0,
				take: take,
			}),
			prisma.benefit.count({ where }),
		])

		logger.info(
			`成功获取权益列表，共 ${total} 条记录，当前页 ${benefits.length} 条`,
		)
		return R.ok({ data: benefits, total })
	} catch (error) {
		logger.error("获取权益列表失败:", error)
		return R.error("获取权益列表失败")
	}
})

// 获取单个权益
router.get("/detail", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		logger.warn(`非管理员用户 ${user?.id} 尝试获取权益详情`)
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		// 从查询参数获取id
		const id = request.nextUrl.searchParams.get("id")
		if (!id) {
			logger.warn("获取权益详情时缺少ID参数")
			return R.error("缺少权益ID参数")
		}

		logger.info(`获取权益详情，ID: ${id}`)
		const benefit = await prisma.benefit.findUnique({
			where: { id },
		})

		if (!benefit) {
			logger.warn(`未找到ID为 ${id} 的权益`)
			return R.notFound("权益不存在")
		}

		logger.info(`成功获取权益详情: ${benefit.code}`)
		return R.ok(benefit)
	} catch (error) {
		logger.error(
			`获取权益详情失败，ID: ${request.nextUrl.searchParams.get("id")}:`,
			error,
		)
		return R.error("获取权益详情失败")
	}
})

// 创建权益
router.post("/", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		logger.warn(`非管理员用户 ${user?.id} 尝试创建权益`)
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		const body = await request.json()
		logger.info(`创建权益请求数据:`, body)
		const validatedData = benefitSchema.parse(body)

		// 检查代码是否已存在
		const existingBenefit = await prisma.benefit.findUnique({
			where: { code: validatedData.code },
		})

		if (existingBenefit) {
			logger.warn(`创建权益失败: 权益代码 ${validatedData.code} 已存在`)
			return R.error("权益代码已存在")
		}

		// 创建权益
		const benefit = await prisma.benefit.create({
			data: validatedData,
		})

		logger.info(`成功创建权益: ${benefit.code}, ID: ${benefit.id}`)
		return R.ok(benefit)
	} catch (error) {
		if (error instanceof z.ZodError) {
			logger.warn(`创建权益数据验证失败:`, error.errors)
			return R.error("数据验证失败")
		}

		logger.error("创建权益失败:", error)
		return R.error("创建权益失败")
	}
})

// 更新权益
router.put("/update", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		logger.warn(`非管理员用户 ${user?.id} 尝试更新权益`)
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		// 从查询参数获取id
		const id = request.nextUrl.searchParams.get("id")
		if (!id) {
			logger.warn("更新权益时缺少ID参数")
			return R.error("缺少权益ID参数")
		}

		const body = await request.json()
		logger.info(`更新权益请求数据, ID: ${id}:`, body)
		const validatedData = benefitSchema.parse(body)

		// 检查权益是否存在
		const existingBenefit = await prisma.benefit.findUnique({
			where: { id },
		})

		if (!existingBenefit) {
			logger.warn(`更新权益失败: ID为 ${id} 的权益不存在`)
			return R.notFound("权益不存在")
		}

		// 更新权益
		const benefit = await prisma.benefit.update({
			where: { id },
			data: {
				name: validatedData.name,
				description: validatedData.description,
				cost: validatedData.cost,
				costRules: validatedData.costRules,
				// 不更新code字段，因为它是唯一标识
			},
		})

		logger.info(`成功更新权益: ${benefit.code}, ID: ${benefit.id}`)
		return R.ok(benefit)
	} catch (error) {
		if (error instanceof z.ZodError) {
			logger.warn(`更新权益数据验证失败:`, error.errors)
			return R.error("数据验证失败")
		}

		logger.error(
			`更新权益失败, ID: ${request.nextUrl.searchParams.get("id")}:`,
			error,
		)
		return R.error("更新权益失败")
	}
})

// 删除权益
router.delete("/delete", async (request: NextRequest) => {
	// 验证用户是否为管理员
	const user = await authUser(true)
	if (user?.role !== UserRole.Admin) {
		logger.warn(`非管理员用户 ${user?.id} 尝试删除权益`)
		return R.forbidden("只有管理员可以访问此API")
	}

	try {
		// 从查询参数获取id
		const id = request.nextUrl.searchParams.get("id")
		if (!id) {
			logger.warn("删除权益时缺少ID参数")
			return R.error("缺少权益ID参数")
		}

		logger.info(`尝试删除权益, ID: ${id}`)
		// 检查权益是否存在
		const existingBenefit = await prisma.benefit.findUnique({
			where: { id },
			include: {
				productRules: true,
				userBenefits: true,
			},
		})

		if (!existingBenefit) {
			logger.warn(`删除权益失败: ID为 ${id} 的权益不存在`)
			return R.notFound("权益不存在")
		}

		// 检查是否有关联数据
		if (
			existingBenefit.productRules.length > 0 ||
			existingBenefit.userBenefits.length > 0
		) {
			logger.warn(
				`删除权益失败: 权益 ${existingBenefit.code} 已被产品或用户使用，无法删除`,
			)
			return R.error("该权益已被产品或用户使用，无法删除")
		}

		// 删除权益
		await prisma.benefit.delete({
			where: { id },
		})

		logger.info(`成功删除权益: ${existingBenefit.code}, ID: ${id}`)
		return R.ok({ success: true })
	} catch (error) {
		logger.error(
			`删除权益失败, ID: ${request.nextUrl.searchParams.get("id")}:`,
			error,
		)
		return R.error("删除权益失败")
	}
})

export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const PUT = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
