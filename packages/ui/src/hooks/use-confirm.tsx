"use client"

import { useState, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  buttonVariants,
} from "@repo/ui/components"
import { cn } from "@repo/utils/react"

interface ConfirmOptions {
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}

interface ConfirmDialogProps extends ConfirmOptions {
  open: boolean
  onConfirm: () => void
  onCancel: () => void
}

/**
 * 确认对话框组件
 */
function ConfirmDialog({
  open,
  onConfirm,
  onCancel,
  title = "确认操作",
  description = "确定要执行此操作吗？",
  confirmText = "确认",
  cancelText = "取消",
  variant = "default",
}: ConfirmDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel()}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onCancel}>{cancelText}</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className={cn(buttonVariants({ variant }))}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

/**
 * 确认对话框 Hook
 *
 * 用法示例:
 * ```tsx
 * const { ConfirmDialog, confirm } = useConfirm();
 *
 * const handleDelete = async () => {
 *   const confirmed = await confirm({
 *     title: "确认删除",
 *     description: "确定要删除此项吗？此操作不可撤销。",
 *     confirmText: "删除",
 *     cancelText: "取消",
 *     variant: "destructive",
 *   });
 *
 *   if (confirmed) {
 *     // 执行删除操作
 *   }
 * };
 *
 * return (
 *   <>
 *     {ConfirmDialog}
 *     <Button onClick={handleDelete}>删除</Button>
 *   </>
 * );
 * ```
 */
export function useConfirm() {
  const [open, setOpen] = useState(false)
  const [options, setOptions] = useState<ConfirmOptions>({})
  const [resolveRef, setResolveRef] = useState<(value: boolean) => void>()

  const confirm = useCallback(
    (options: ConfirmOptions = {}) => {
      return new Promise<boolean>((resolve) => {
        setOptions(options)
        setResolveRef(() => resolve)
        setOpen(true)
      })
    },
    []
  )

  const handleConfirm = useCallback(() => {
    if (resolveRef) {
      resolveRef(true)
    }
    setOpen(false)
  }, [resolveRef])

  const handleCancel = useCallback(() => {
    if (resolveRef) {
      resolveRef(false)
    }
    setOpen(false)
  }, [resolveRef])

  const ConfirmDialogComponent = (
    <ConfirmDialog
      open={open}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      {...options}
    />
  )

  return {
    confirm,
    ConfirmDialog: ConfirmDialogComponent,
  }
}
