"use client"

import { useContext } from "react"
import { FilterContext } from "../components/data-table/data-table-filter"
import { Column, ColumnFilter } from "@tanstack/react-table"

interface FilterIntegrationOptions {
	name: string
}

interface FilterIntegrationResult {
	value: any
	onChange: (value: any) => void
	onValueChange: (value: any) => void
}

/**
 * 表格过滤器集成钩子
 *
 * 用于将任何组件与数据表格过滤器集成
 *
 * @example
 * ```tsx
 * function CustomFilterField({ name }) {
 *   const { value, onChange, onValueChange } = useFilterIntegration({ name });
 *
 *   // 对于标准表单元素
 *   return <input value={value} onChange={onChange} />;
 *
 *   // 或者对于 shadcn-ui 组件
 *   return <Select value={value} onValueChange={onValueChange} />;
 * }
 * ```
 */
export function useFilterIntegration({
	name,
}: FilterIntegrationOptions): FilterIntegrationResult {
	const { table } = useContext(FilterContext)

	let column: Column<any, any> | undefined
	try {
		// 尝试获取列，如果不存在则创建一个虚拟过滤器状态
		column = table?.getColumn(name)
	} catch (error) {
		// ignore
		column = undefined
	}
	const value = column ? (column.getFilterValue() ?? "") : ""

	const handleChange = (e: any) => {
		const newValue = e.target?.value !== undefined ? e.target.value : e
		if (column) {
			column.setFilterValue(newValue)
		} else if (table) {
			// 如果列不存在但需要过滤，可以使用columnFilters状态
			const currentFilters = table.getState().columnFilters || []
			const newFilters = currentFilters.filter(
				(filter: ColumnFilter) => filter.id !== name,
			)
			if (newValue) {
				newFilters.push({ id: name, value: newValue })
			}
			table.setColumnFilters(newFilters)
		}
	}

	const handleValueChange = (newValue: any) => {
		if (column) {
			column.setFilterValue(newValue)
		} else if (table) {
			// 如果列不存在但需要过滤，可以使用columnFilters状态
			const currentFilters = table.getState().columnFilters || []
			const newFilters = currentFilters.filter(
				(filter: ColumnFilter) => filter.id !== name,
			)
			if (newValue) {
				newFilters.push({ id: name, value: newValue })
			}
			table.setColumnFilters(newFilters)
		}
	}

	return {
		value,
		onChange: handleChange,
		onValueChange: handleValueChange,
	}
}
