"use client"

import { type VariantProps, cva } from "class-variance-authority"
import * as React from "react"

import { cn } from "@repo/utils/react"

const spinnerVariants = cva(
	"inline-flex items-center justify-center animate-spin text-current",
	{
		variants: {
			size: {
				xs: "size-3",
				sm: "size-4",
				default: "size-5",
				md: "size-6",
				lg: "size-8",
				xl: "size-10",
			},
			color: {
				default: "text-foreground",
				primary: "text-primary",
				secondary: "text-secondary",
				muted: "text-muted-foreground",
				accent: "text-accent",
			},
			variant: {
				default: "",
				fullscreen:
					"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",
			},
		},
		defaultVariants: {
			size: "default",
			color: "default",
			variant: "default",
		},
	},
)

export interface SpinnerProps
	extends Omit<React.HTMLAttributes<HTMLDivElement>, "color">,
		VariantProps<typeof spinnerVariants> {
	/**
	 * 是否显示文本
	 */
	showText?: boolean
	/**
	 * 加载文本
	 */
	text?: string
	/**
	 * 文本类名
	 */
	textClassName?: string
}

/**
 * 加载中组件
 *
 * 显示一个加载动画，支持不同大小和颜色变体
 *
 * @example
 * // 基础用法
 * <Spinner />
 *
 * // 带文本
 * <Spinner showText text="加载中..." />
 *
 * // 不同大小
 * <Spinner size="xs" />
 * <Spinner size="sm" />
 * <Spinner size="md" />
 * <Spinner size="lg" />
 * <Spinner size="xl" />
 *
 * // 不同颜色
 * <Spinner color="primary" />
 * <Spinner color="secondary" />
 * <Spinner color="muted" />
 * <Spinner color="accent" />
 *
 * // 全屏加载
 * <Spinner variant="fullscreen" />
 */
export function Spinner({
	className,
	size,
	color,
	variant,
	showText = true,
	text = "加载中",
	textClassName,
	...props
}: SpinnerProps) {
	const SpinnerContent = (
		<div className="flex items-center gap-2">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
				className={cn(
					spinnerVariants({ size, color }),
					variant === "default" ? className : undefined,
				)}
			>
				<title>Loading</title>
				<circle
					cx="12"
					cy="12"
					r="10"
					stroke="currentColor"
					strokeOpacity="0.25"
				/>
				<path
					d="M12 2C6.47715 2 2 6.47715 2 12C2 14.7255 3.09032 17.1962 4.85857 19"
					stroke="currentColor"
					strokeOpacity="0.75"
				/>
			</svg>
			{showText && (
				<span className={cn("text-sm font-medium", textClassName)}>{text}</span>
			)}
		</div>
	)

	if (variant === "fullscreen") {
		return (
			<div className={cn(spinnerVariants({ variant }), className)} {...props}>
				{SpinnerContent}
			</div>
		)
	}

	return SpinnerContent
}
