datasource db {
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_DATABASE_URL")
  provider  = "postgresql"
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin", "linux-musl", "rhel-openssl-1.1.x", "debian-openssl-1.1.x", "debian-openssl-1.0.x"]
}

model User {
  /// @comment 用户ID
  id                    String                @id @default(cuid()) @map("id")
  /// @comment 用户邮箱
  email                 String                @unique @map("email")
  /// @comment 邮箱验证状态
  emailVerified         Boolean?              @map("email_verified")
  /// @comment 用户状态
  status                String                @default("ACTIVE") @map("status")
  /// @comment 角色
  role                  String                @default("USER") @map("role")
  /// @comment 资源权限
  resourcePermissions   String[]              @map("resource_permissions") // 存储额外的资源权限ID列表
  /// @comment 用户名
  name                  String?               @map("name")
  /// @comment 用户头像
  avatar                String?               @map("avatar")
  /// @comment 用户密码
  password              String?               @map("password")
  /// @comment 用户信用点数
  credits               Int                   @default(0) @map("credits")
  /// @comment 用户GitHub ID
  githubId              String?               @map("github_id")
  /// @comment 用户GitHub Access Token
  githubAccessToken     String?               @map("github_access_token")
  /// @comment 用户GitHub Token
  githubToken           String?               @map("github_token")
  /// @comment 可用项目数
  availableProjectCount Int                   @default(0) @map("available_project_count")
  /// @comment 已用项目数
  usedProjectCount      Int                   @default(0) @map("used_project_count")
  /// @comment 用户优惠码
  promoCode             String?               @unique @map("promo_code") @db.VarChar(10)
  /// @comment 创建时间
  createdAt             DateTime              @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt             DateTime              @updatedAt @map("updated_at")
  /// @comment 网站
  websites              Website[]
  /// @comment 项目
  projects              Project[]
  /// @comment 项目使用记录
  projectCountHistories ProjectCountHistory[]
  /// @comment 用户推广
  userPromotions        UserPromotion[]
  /// @comment 用户游戏库
  userGameLibraries     UserGameLibrary[]
  /// @comment 游戏任务记录
  gameTaskRecords       GameTaskRecord[]
  /// @comment 提交任务
  submitTasks           SubmissionTask[]
  /// @comment 会话
  sessions              Session[]
  /// @comment 账号
  accounts              Account[]
  /// @comment 交易记录
  trades                Trade[]
  /// @comment 权益
  benefits              UserBenefit[]
  /// @comment 权益使用记录
  benefitUsages         UserBenefitUsage[]
  /// @comment 趋势用户关键词
  trendUserKeywords     TrendUserKeyword[]
  /// @comment 趋势关键词收藏
  trendFavorites        TrendFavorite[]

  @@map("t_user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("s_session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("s_account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("s_verification")
}

model Jwks {
  id         String   @id
  publicKey  String
  privateKey String
  createdAt  DateTime

  @@map("s_jwks")
}

model UserGameLibrary {
  /// @comment ID
  id              String  @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId          String  @map("user_id")
  /// @comment 游戏名称
  gameName        String  @map("game_name")
  /// @comment 游戏描述
  gameDescription String? @map("game_description") @db.Text
  /// @comment 游戏类型
  gameType        String  @default("iframe") @map("game_type")

  /// @comment 游戏iframe URL
  gameIframeUrl String? @map("game_iframe_url") @db.VarChar(500)
  /// @comment 游戏截图URL
  screenshotUrl String? @map("screenshot_url") @db.VarChar(500)

  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  /// @comment 游戏来源
  gameSource String @default("import") @map("game_source")

  // 关联
  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("t_user_game_library")
}

model PublicGameLibrary {
  /// @comment ID
  id              String  @id @default(cuid()) @map("id")
  /// @comment 游戏名称
  gameName        String  @map("game_name")
  /// @comment 游戏描述
  gameDescription String? @map("game_description") @db.Text
  /// @comment 游戏类型
  gameType        String  @default("iframe") @map("game_type")

  /// @comment 游戏iframe URL
  gameIframeUrl String? @map("game_iframe_url") @db.VarChar(500)
  /// @comment 游戏截图URL
  screenshotUrl String? @map("screenshot_url") @db.VarChar(500)
  /// @comment 游戏图标URL
  iconUrl       String? @map("icon_url") @db.VarChar(500)

  /// @comment 游戏来源
  gameSource       String  @map("game_source")
  /// @comment 游戏原始分类
  originalCategory String? @map("original_category")
  /// @comment 本地分类
  localCategory    String? @map("local_category")
  /// @comment 游戏状态
  status           String  @default("PENDING") @map("status")
  /// @comment 元数据（JSON格式）
  metadata         Json?   @map("metadata")

  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("t_public_game_library")
}

model Website {
  /// @comment 网站ID
  id                     String    @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId                 String    @map("user_id")
  /// @comment 网站URL
  url                    String    @map("url")
  /// @comment 网站标题
  title                  String    @map("title")
  /// @comment 网站Logo
  logo                   String?   @map("logo")
  /// @comment 网站描述
  description            String?   @map("description")
  /// @comment 网站类别
  category               String?   @map("category")
  /// @comment 每日提交限制
  dailySubmissionLimit   Int       @default(10) @map("daily_submission_limit")
  /// @comment 总提交限制
  totalSubmissionLimit   Int       @default(100) @map("total_submission_limit")
  /// @comment 每日开始提交时间
  submissionStartTime    String    @default("06:00") @map("submission_start_time")
  /// @comment 每日结束提交时间
  submissionEndTime      String    @default("23:00") @map("submission_end_time")
  /// @comment 开始提交日期
  submissionStartDate    DateTime  @default(now()) @map("submission_start_date")
  /// @comment 提交网站类型
  submissionWebsiteTypes String[]  @map("submission_website_types")
  /// @comment 下次提交时间
  nextSubmissionTime     DateTime? @map("next_submission_time")
  /// @comment 是否激活
  isActive               Boolean   @default(true) @map("is_active")
  /// @comment 创建时间
  createdAt              DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt              DateTime  @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id])

  @@map("t_website")
}

model BacklinkWebsite {
  /// @comment 外链网站ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 外链网站URL
  url       String   @map("url")
  /// @comment 标签
  tags      String[] @map("tags")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("t_backlink_website")
}

model SubmissionTask {
  /// @comment 提交记录ID
  id              String             @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId          String             @map("user_id")
  /// @comment 提交邮箱
  email           String?            @map("email")
  /// @comment 提交URL
  siteUrl         String             @map("site_url")
  /// @comment 提交网站Logo
  siteLogoUrl     String?            @map("site_logo_url")
  /// @comment 提交标题
  siteTitle       String             @map("site_title")
  /// @comment 提交描述
  siteDescription String             @map("site_description")
  /// @comment 提交状态
  status          String             @default("PENDING") @map("status")
  /// @comment 成功数量
  succeedCount    Int                @default(0) @map("succeed_count")
  /// @comment 失败数量
  failedCount     Int                @default(0) @map("failed_count")
  /// @comment 总数量
  totalCount      Int                @default(0) @map("total_count")
  /// @comment 错误信息
  errorMessage    String?            @map("error_message")
  /// @comment 创建时间
  createdAt       DateTime           @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt       DateTime           @updatedAt @map("updated_at")
  user            User               @relation(fields: [userId], references: [id])
  records         SubmissionRecord[]

  @@map("t_submission_task")
}

model SubmissionRecord {
  /// @comment 提交记录ID
  id           String         @id @default(cuid()) @map("id")
  /// @comment 提交任务ID
  taskId       String         @map("task_id")
  /// @comment 外链目录ID
  backlinkId   String         @map("backlink_id")
  /// @comment 外链URL
  backlinkUrl  String         @map("backlink_url")
  /// @comment 外链标签
  backlinkTags String[]       @map("backlink_tags")
  /// @comment 提交状态
  status       String         @default("PENDING") @map("status")
  /// @comment 错误信息
  errorMessage String?        @map("error_message")
  /// @comment 提交结果
  resultUrl    String?        @map("result_url")
  /// @comment 创建时间
  createdAt    DateTime       @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt    DateTime       @updatedAt @map("updated_at")
  task         SubmissionTask @relation(fields: [taskId], references: [id])

  @@map("t_submission_record")
}

model UserAccount {
  /// @comment 账号ID
  id         String    @id @default(cuid()) @map("id")
  /// @comment 所有全
  ownership  String    @map("ownership")
  /// @comment 用户名
  username   String    @map("username")
  /// @comment 密码（应该加密存储）
  password   String    @map("password")
  /// @comment 账号状态
  status     String    @default("ACTIVE") @map("status")
  /// @comment 上次使用时间
  lastUsedAt DateTime? @map("last_used_at")
  /// @comment 创建时间
  createdAt  DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt  DateTime  @updatedAt @map("updated_at")

  @@map("t_account")
}

model ProjectAccess {
  /// @comment 记录ID
  id         String             @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId  String             @map("project_id")
  /// @comment 被授权的用户邮箱
  userEmail  String             @map("user_email")
  /// @comment 创建时间
  createdAt  DateTime           @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt  DateTime           @updatedAt @map("updated_at")
  /// @comment 访问记录
  accessLogs ProjectAccessLog[]
  /// @comment 项目关联
  project    Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, userEmail])
  @@map("t_project_access")
}

model ProjectAccessLog {
  /// @comment 记录ID
  id              String         @id @default(cuid()) @map("id")
  /// @comment 项目访问ID
  projectAccessId String?        @map("project_access_id")
  /// @comment 项目ID
  projectId       String         @map("project_id")
  /// @comment 访问用户邮箱
  userEmail       String         @map("user_email")
  /// @comment 访问IP
  ipAddress       String?        @map("ip_address")
  /// @comment 访问时间
  accessTime      DateTime       @default(now()) @map("access_time")
  /// @comment 项目权限关联
  projectAccess   ProjectAccess? @relation(fields: [projectAccessId], references: [id], onDelete: SetNull)
  /// @comment 项目关联
  project         Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("t_project_access_log")
}

// 游戏项目
model Project {
  /// @comment 项目ID
  id                  String    @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId              String    @map("user_id")
  /// @comment 项目名称
  name                String    @map("name")
  templateCode        String    @map("template_code")
  /// @comment 项目描述
  description         String?   @map("description")
  /// @comment 模板版本
  templateVersion     String?   @map("template_version")
  /// @comment 模板类型
  templateType        String?   @map("template_type")
  /// @comment 模板URL
  templateUrl         String?   @map("template_url")
  /// @comment 模板类型
  templateCategory    String?   @map("template_category")
  /// @comment 部署主机类型
  hostType            String    @map("host_type")
  /// @comment 服务器ID（部署后固定）
  serverId            String?   @map("server_id")
  /// @comment Cloudflare账号ID（部署后固定）
  cloudflareAccountId String?   @map("cloudflare_account_id")
  /// @comment 子域名（部署后固定）
  subdomainName       String?   @map("subdomain_name")
  /// @comment 是否已部署
  isDeployed          Boolean   @default(false) @map("is_deployed")
  /// @comment 最后部署时间
  lastDeployedAt      DateTime? @map("last_deployed_at")
  ftpUserName         String?   @map("ftp_user_name")
  ftpPassword         String?   @map("ftp_password")
  ftpHomePath         String?   @map("ftp_home_path")
  /// @comment BT站点ID
  btSiteId            Int?      @map("bt_site_id")
  /// @comment 创建时间
  createdAt           DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt           DateTime  @updatedAt @map("updated_at")

  user              User                      @relation(fields: [userId], references: [id])
  /// @comment 项目访问权限
  projectAccesses   ProjectAccess[]
  /// @comment 项目访问日志
  accessLogs        ProjectAccessLog[]
  /// @comment 游戏任务记录
  gameTaskRecords   GameTaskRecord[]
  /// @comment 部署记录
  deploymentRecords ProjectDeploymentRecord[]
  /// @comment 服务器关联
  server            Server?                   @relation(fields: [serverId], references: [id])
  /// @comment Cloudflare账号关联
  cloudflareAccount CloudflareAccount?        @relation(fields: [cloudflareAccountId], references: [id])
  /// @comment 域名绑定
  projectDomains    ProjectDomain[]
  /// @comment 网站设置
  siteSettings      ProjectSiteSetting[]

  @@map("t_project")
}

model ProjectCountHistory {
  /// @comment 记录ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId    String   @map("user_id")
  /// @comment 操作类型 (increase/decrease)
  action    String   @map("action")
  /// @comment 变更数量
  amount    Int      @default(1) @map("amount")
  /// @comment 备注说明
  remark    String?  @map("remark")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 关联用户
  user      User     @relation(fields: [userId], references: [id])

  @@map("t_project_count_history")
}

model GameItem {
  /// @comment 游戏ID
  id              String   @id @default(cuid()) @map("id")
  /// @comment 原始标题
  rawTitle        String   @map("raw_title")
  /// @comment AI清洗后的游戏名称
  cleanedName     String?  @map("cleaned_name")
  /// @comment 数据来源
  source          String   @map("source")
  /// @comment 原始URL
  sourceUrl       String   @unique @map("source_url")
  /// @comment 状态
  status          String   @default("RAW") @map("status")
  /// @comment 清洗重试次数
  cleanRetryCount Int      @default(0) @map("clean_retry_count")
  /// @comment 关联的游戏主数据ID
  masterGameId    String?  @map("master_game_id")
  /// @comment 创建时间
  createdAt       DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联
  masterGame MasterGame? @relation(fields: [masterGameId], references: [id])

  @@index([status])
  @@index([source])
  @@index([cleanedName])
  @@map("t_game_item")
}

model MasterGame {
  /// @comment 游戏ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 游戏名称
  name        String   @unique @map("name")
  /// @comment 来源数量
  sourceCount Int      @default(1) @map("source_count")
  /// @comment 来源列表 ["poki.com", "itch.io"]
  sources     String[] @map("sources")
  /// @comment 标签列表 ["trending_up", "featured"]
  tags        String[] @default([]) @map("tags")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  gameItems      GameItem[]
  trendData      GameTrendData[]
  socialTasks    SocialMediaTask[]
  socialContents SocialMediaContent[]
  dailyStats     SocialMediaDailyStats[]

  @@index([name])
  @@map("t_master_game")
}

model GameTrendData {
  /// @comment ID
  id                 String   @id @default(cuid()) @map("id")
  /// @comment 关联的游戏ID
  masterGameId       String   @map("master_game_id")
  /// @comment 数据日期
  date               DateTime @map("date")
  /// @comment 时间范围(7/30)
  dayRange           Int      @map("day_range")
  /// @comment 趋势状态（用于web查询）
  status             String   @default("PENDING") @map("status")
  /// @comment 抓取状态（用于scheduler）
  fetchStatus        String   @default("PENDING") @map("fetch_status")
  /// @comment 趋势分析状态（用于web查询）
  analyzedStatus     Boolean  @default(false) @map("analyzed_status")
  /// @comment 分析处理状态（用于scheduler）
  analyzeStatus      Boolean  @default(false) @map("analyze_status")
  /// @comment 趋势JSON数据
  trendData          Json?    @map("trend_data")
  /// @comment 对比数据JSON
  compareData        Json?    @map("compare_data")
  /// @comment 平均趋势值
  avgTrendValue      Float?   @map("avg_trend_value")
  /// @comment 平均对比值
  avgCompareValue    Float?   @map("avg_compare_value")
  /// @comment 趋势值字符串(逗号分隔)
  trendValueString   String?  @map("trend_value_string")
  /// @comment 对比值字符串(逗号分隔)
  compareValueString String?  @map("compare_value_string")
  /// @comment 重试次数
  retryCount         Int      @default(0) @map("retry_count")
  /// @comment 创建时间
  createdAt          DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt          DateTime @updatedAt @map("updated_at")

  // 关联
  masterGame MasterGame @relation(fields: [masterGameId], references: [id])

  @@unique([masterGameId, date, dayRange])
  @@index([status])
  @@index([fetchStatus])
  @@index([analyzedStatus])
  @@index([analyzeStatus])
  @@index([date])
  @@map("t_game_trend_data")
}

model SocialMediaTask {
  /// @comment ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 游戏ID
  gameId    String   @map("game_id")
  /// @comment 平台
  platform  String   @map("platform")
  /// @comment 任务日期
  taskDate  DateTime @map("task_date")
  /// @comment 状态
  status    String   @default("PENDING") @map("status")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  masterGame MasterGame                  @relation(fields: [gameId], references: [id])
  contents   SocialMediaContentHistory[]
  dailyStats SocialMediaDailyStats?

  @@map("t_social_media_task")
}

model SocialMediaContent {
  /// @comment ID
  id           String   @id @default(cuid()) @map("id")
  /// @comment 游戏ID
  gameId       String   @map("game_id")
  /// @comment 平台
  platform     String   @map("platform")
  /// @comment 内容ID（如YouTube视频ID）
  contentId    String   @map("content_id")
  /// @comment 标题
  title        String   @map("title")
  /// @comment 描述
  description  String?  @map("description")
  /// @comment 频道名称
  channel      String?  @map("channel")
  /// @comment URL
  url          String   @map("url")
  /// @comment 缩略图URL
  thumbnailUrl String?  @map("thumbnail_url")
  /// @comment 点击量
  views        Int      @default(0) @map("views")
  /// @comment 发布时间
  publishedAt  DateTime @map("published_at")
  /// @comment 首次发现时间
  firstFoundAt DateTime @default(now()) @map("first_found_at")
  /// @comment 创建时间
  createdAt    DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt    DateTime @updatedAt @map("updated_at")

  masterGame MasterGame                  @relation(fields: [gameId], references: [id])
  history    SocialMediaContentHistory[]

  @@unique([platform, contentId])
  @@map("t_social_media_content")
}

model SocialMediaContentHistory {
  /// @comment ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 内容ID
  contentId String   @map("content_id")
  /// @comment 任务ID
  taskId    String   @map("task_id")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")

  content SocialMediaContent @relation(fields: [contentId], references: [id])
  task    SocialMediaTask    @relation(fields: [taskId], references: [id])

  @@unique([contentId, taskId])
  @@map("t_social_media_content_history")
}

model SocialMediaDailyStats {
  /// @comment ID
  id         String   @id @default(cuid()) @map("id")
  /// @comment 游戏ID
  gameId     String   @map("game_id")
  /// @comment 任务ID
  taskId     String   @unique @map("task_id")
  /// @comment 平台
  platform   String   @map("platform")
  /// @comment 统计日期
  statsDate  DateTime @map("stats_date")
  /// @comment 总内容数
  totalCount Int      @default(0) @map("total_count")
  /// @comment 新增内容数
  newCount   Int      @default(0) @map("new_count")
  /// @comment 创建时间
  createdAt  DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt  DateTime @updatedAt @map("updated_at")

  masterGame MasterGame      @relation(fields: [gameId], references: [id])
  task       SocialMediaTask @relation(fields: [taskId], references: [id])

  @@unique([gameId, platform, statsDate])
  @@index([gameId, platform, statsDate])
  @@map("t_social_media_daily_stats")
}

model UserPromotion {
  /// @comment 推广ID
  id         String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId     String   @map("user_id")
  /// @comment 服务商ID
  mediumId   String   @default("FaFaFa") @map("medium_id")
  /// @comment 站长ID
  campaignId String?  @map("campaign_id")
  /// @comment 密钥，接口调用需要
  secret     String?  @map("secret")
  /// @comment 创建时间
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation(fields: [userId], references: [id])

  @@map("t_user_promotion")
}

// 游戏任务记录模型
model GameTaskRecord {
  /// @comment 任务ID
  id                 String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId             String   @map("user_id")
  /// @comment 任务名称
  name               String   @map("name")
  /// @comment 任务状态
  status             String   @default("PENDING") @map("status")
  /// @comment 任务消息
  message            String?  @map("message")
  /// @comment 错误信息
  error              Json?    @map("error")
  /// @comment 用户输入文本
  user_input_text    String?  @map("user_input_text")
  /// @comment 是否为子游戏
  is_sub_game        Boolean  @default(false) @map("is_sub_game")
  /// @comment 是否覆盖页面路径
  override_page_path Boolean  @default(false) @map("override_page_path")
  /// @comment 当前步骤
  currentStep        String?  @map("current_step")
  /// @comment 自动部署
  auto_deploy        Boolean  @default(false) @map("auto_deploy")
  /// @comment 游戏类型
  gameType           String?  @map("game_type")
  /// @comment 游戏下载链接
  gameDownload       String?  @map("game_download")
  /// @comment 背景类型
  bgType             String?  @map("bg_type")
  /// @comment 背景图片
  bgImage            String?  @map("bg_image")
  /// @comment 背景视频
  bgVideo            String?  @map("bg_video")
  /// @comment 游戏iframe URL
  gameIframeUrl      String?  @map("game_iframe_url")
  /// @comment 截图URL
  screenshotUrl      String?  @map("screenshot_url")
  /// @comment 创建时间
  createdAt          DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt          DateTime @updatedAt @map("updated_at")
  /// @comment 关联的项目ID
  projectId          String   @map("project_id")
  /// @comment 任务类型
  taskType           String   @default("GAME_TASK") @map("task_type")
  /// @comment 任务数据
  taskData           String?  @map("task_data") @db.Text

  // 关联关系
  project Project @relation(fields: [projectId], references: [id])
  user    User    @relation(fields: [userId], references: [id])

  @@index([projectId])
  @@index([status])
  @@index([createdAt])
  @@index([userId])
  @@map("t_game_task_record")
}

// 公共游戏库爬虫任务记录模型
model PublicGameCrawlTask {
  /// @comment 任务ID
  id             String    @id @default(cuid()) @map("id")
  /// @comment 爬虫源
  source         String    @map("source")
  /// @comment 爬虫类型（全量或最新）
  crawlType      String    @map("crawl_type")
  /// @comment 爬虫状态
  status         String    @default("PENDING") @map("status")
  /// @comment 爬虫消息
  message        String?   @map("message")
  /// @comment 爬虫网址
  url            String?   @map("url")
  /// @comment 爬虫开始时间
  startTime      DateTime? @map("start_time")
  /// @comment 爬虫结束时间
  endTime        DateTime? @map("end_time")
  /// @comment 总游戏数
  totalGames     Int       @default(0) @map("total_games")
  /// @comment 成功数
  successCount   Int       @default(0) @map("success_count")
  /// @comment 重复数
  duplicateCount Int       @default(0) @map("duplicate_count")
  /// @comment 失败数
  failedCount    Int       @default(0) @map("failed_count")
  /// @comment 错误信息
  error          String?   @map("error") @db.Text
  /// @comment 创建时间
  createdAt      DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt      DateTime  @updatedAt @map("updated_at")

  @@index([status])
  @@index([createdAt])
  @@index([source])
  @@map("t_public_game_crawl_task")
}

// 服务器模型
model Server {
  /// @comment 服务器ID
  id         String @id @default(cuid()) @map("id")
  /// @comment 服务器名称
  name       String @map("name")
  /// @comment IP地址
  ipAddress  String @map("ip_address")
  /// @comment 宝塔面板URL
  btPanelUrl String @map("bt_panel_url")

  /// @comment 宝塔面板API密钥
  btPanelApiKey        String   @map("bt_panel_api_key")
  /// @comment SSH用户名
  sshUsername          String   @map("ssh_username")
  /// @comment SSH密码
  sshPassword          String   @map("ssh_password")
  /// @comment SSH私钥
  sshPrivateKey        String   @map("ssh_private_key")
  ///@comment 网站基础子域名
  baseDomain           String?  @map("base_domain")
  /// @comment 已部署网站数量
  deployedWebsiteCount Int      @default(0) @map("deployed_website_count")
  /// @comment 最大网站数量
  maxWebsiteCount      Int      @map("max_website_count")
  /// @comment 创建时间
  createdAt            DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt            DateTime @updatedAt @map("updated_at")

  // 关联
  projects          Project[]
  deploymentRecords ProjectDeploymentRecord[]

  @@map("t_server")
}

// Cloudflare账号模型
model CloudflareAccount {
  /// @comment Cloudflare账号ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 账号ID
  accountId   String   @map("account_id")
  /// @comment 邮箱
  email       String   @map("email")
  /// @comment 域名管理Token
  domainToken String?  @map("domain_token")
  /// @comment 当前域名数量
  domainCount Int      @default(0) @map("domain_count")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联
  projects          Project[]
  deploymentRecords ProjectDeploymentRecord[]

  @@map("t_cloudflare_account")
}

// 项目部署记录模型
model ProjectDeploymentRecord {
  /// @comment 部署记录ID
  id                  String    @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId           String    @map("project_id")
  /// @comment 服务器ID
  serverId            String?   @map("server_id")
  /// @comment Cloudflare账号ID
  cloudflareAccountId String?   @map("cloudflare_account_id")
  /// @comment 子域名
  subdomainName       String?   @map("subdomain_name")
  /// @comment 部署状态
  status              String    @default("PENDING") @map("status")
  /// @comment 部署消息
  message             String?   @map("message")
  /// @comment 错误信息
  error               String?   @map("error") @db.Text
  /// @comment 部署开始时间
  startTime           DateTime  @default(now()) @map("start_time")
  /// @comment 部署完成时间
  endTime             DateTime? @map("end_time")
  /// @comment 部署日志路径
  logFilePath         String?   @map("log_file_path")
  /// @comment 部署者ID
  deployedBy          String?   @map("deployed_by")
  /// @comment 消息ID
  messageId           String?   @map("message_id")
  /// @comment 创建时间
  createdAt           DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // 关联
  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  server            Server?            @relation(fields: [serverId], references: [id])
  cloudflareAccount CloudflareAccount? @relation(fields: [cloudflareAccountId], references: [id])

  @@index([projectId])
  @@index([status])
  @@index([startTime])
  @@map("t_project_deployment_record")
}

// 项目域名绑定模型
model ProjectDomain {
  /// @comment 域名ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId   String   @map("project_id")
  /// @comment 域名
  domain      String   @map("domain")
  /// @comment 域名状态 (active/pending/error)
  status      String   @default("pending") @map("status")
  /// @comment 域名类型（temp/main）
  type        String   @default("MAIN") @map("type")
  zoneId      String?  @map("zone_id")
  nameServer1 String?  @map("name_server1")
  nameServer2 String?  @map("name_server2")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([domain])
  @@map("t_project_domain")
}

// 交易记录
model Trade {
  // @comment 主键
  id              String        @id @default(cuid())
  // @comment 交易号，唯一标识
  tradeNo         String        @unique @map("trade_no")
  // @comment 交易类型（微信、支付宝等）
  type            String        @map("type")
  // @comment 支付方式 (JSAPI、H5、APP)
  method          String        @map("method")
  // @comment 交易状态 (pending/success/failed)
  status          String        @default("pending") @map("status")
  // @comment 交易失败原因
  reason          String?       @map("reason")
  // @comment 交易金额（分）
  amount          Int           @map("amount")
  // @comment 交易货币
  currency        String        @default("CNY") @map("currency")
  // @comment 商品ID
  goodsId         String        @map("goods_id")
  // @comment 商品描述
  description     String?       @map("description")
  // @comment 用户ID
  userId          String        @map("user_id")
  // @comment 客户端IP
  clientIp        String?       @map("client_ip")
  // @comment 支付平台交易号
  platformTradeNo String?       @map("platform_trade_no")
  // @comment 支付二维码URL
  codeUrl         String?       @map("code_url")
  // @comment H5支付URL
  h5Url           String?       @map("h5_url")
  // @comment JSAPI支付参数
  jsapiParams     Json?         @map("jsapi_params")
  // @comment 元数据
  metadata        Json?         @map("metadata")
  // @comment 支付时间
  paidTime        DateTime?     @map("paid_time")
  // @comment 通知URL
  notifyUrl       String?       @map("notify_url")
  // @comment 通知状态
  notifyStatus    String?       @map("notify_status")
  // @comment 最后通知时间
  lastNotifiedAt  DateTime?     @map("last_notified_at")
  // @comment 过期时间
  expiredAt       DateTime?     @map("expired_at")
  // @comment 创建时间
  createdAt       DateTime      @default(now()) @map("created_at")
  // @comment 更新时间
  updatedAt       DateTime      @updatedAt @map("updated_at")
  // @comment 用户关联
  user            User          @relation(fields: [userId], references: [id])
  // @comment 购买记录关联
  benefits        UserBenefit[]

  @@map("t_trade")
}

// 退款记录
model Refund {
  /// @comment ID
  id               String    @id @default(cuid())
  /// @comment 退款号
  refundNo         String    @unique @map("refund_no")
  /// @comment 交易号
  tradeNo          String    @map("trade_no")
  /// @comment 状态
  status           String    @map("status")
  /// @comment 金额
  amount           Int       @map("amount")
  /// @comment 原因
  reason           String?   @map("reason")
  /// @comment 元数据
  metadata         Json?     @map("metadata")
  /// @comment 支付平台退款号
  platformRefundNo String?   @map("platform_refund_no")
  /// @comment 退款时间
  refundedTime     DateTime? @map("refunded_time")
  /// @comment 通知URL
  notifyUrl        String?   @map("notify_url")
  /// @comment 通知状态
  notifyStatus     String?   @map("notify_status")
  /// @comment 最后通知时间
  lastNotifiedAt   DateTime? @map("last_notified_at")
  /// @comment 创建时间
  createdAt        DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt        DateTime  @updatedAt @map("updated_at")

  @@map("t_refund")
}

// 产品模型
model Product {
  /// @comment ID
  id            Int                  @id @default(autoincrement())
  /// @comment 语言
  locale        String               @default("zh-CN") @map("locale") @db.VarChar(10) // 语言
  /// @comment 产品代码
  code          String               @unique @map("code") @db.VarChar(50)
  /// @comment 产品名称
  name          String               @map("name") @db.VarChar(100)
  /// @comment 产品描述
  description   String               @map("description") @db.VarChar(500)
  /// @comment 原价（单位：分）
  price         Int                  @map("price") // 原价（单位：分）
  /// @comment 币种
  currency      String               @default("CNY") @map("currency") @db.VarChar(10) // 币种
  /// @comment 信用点
  credits       Int                  @default(0) @map("credits")
  /// @comment 站点数量
  siteCount     Int                  @default(1) @map("site_count") // 站点数量
  /// @comment 折扣比例，1.0表示无折扣
  discount      Float                @default(1.0) @map("discount") // 折扣比例，1.0表示无折扣
  /// @comment 折扣后价格（单位：分）
  discountPrice Int                  @default(0) @map("discount_price") // 折扣后价格（单位：分）
  /// @comment 功能特性
  features      Json                 @map("features") // 功能特性
  /// @comment 附加元数据，可存储额外信息
  metadata      Json?                @map("metadata") // 附加元数据，可存储额外信息
  /// @comment 是否热门
  isPopular     Boolean              @default(false) @map("is_popular") // 是否热门
  /// @comment 排序顺序
  sortOrder     Int                  @default(0) @map("sort_order") // 排序顺序
  /// @comment 状态
  status        String               @default("ACTIVE") @map("status") @db.VarChar(20) // 状态
  /// @comment 创建时间
  createdAt     DateTime             @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt     DateTime             @updatedAt @map("updated_at")
  /// @comment 订阅记录
  userBenefits  UserBenefit[]
  /// @comment 产品权益关系
  benefitRules  ProductBenefitRule[]

  @@index([code])
  @@map("t_product")
}

// 权益资源表
model Benefit {
  /// @comment 权益资源ID
  id          String   @id @default(cuid())
  /// @comment 权益代码
  code        String   @unique @map("code") @db.VarChar(50)
  /// @comment 权益名称
  name        String   @map("name") @db.VarChar(100)
  /// @comment 权益描述
  description String?  @map("description") @db.VarChar(500)
  /// @comment 消耗信用点数
  cost        Int      @default(0) @map("cost")
  /// @comment 扣除规则
  costRules   String?  @map("cost_rules")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  /// @comment 产品权益规则
  productRules ProductBenefitRule[]
  /// @comment 用户权益
  userBenefits UserBenefit[]
  /// @comment 权益使用记录
  usages       UserBenefitUsage[]

  @@index([code])
  @@map("t_benefit")
}

// 产品权益资源关系表
model ProductBenefitRule {
  /// @comment 规则ID
  id          String   @id @default(cuid())
  /// @comment 产品Code
  productCode String   @map("product_code") @db.VarChar(50)
  /// @comment 权益资源ID
  benefitCode String   @map("benefit_code") @db.VarChar(50)
  /// @comment 最大数量限制 (-1表示无限制)
  maxQuantity Int      @default(-1) @map("max_quantity")
  /// @comment 有效天数 (-1表示永久有效)
  validDays   Int      @default(-1) @map("valid_days")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  /// @comment 产品关联
  product Product @relation(fields: [productCode], references: [code])
  /// @comment 权益资源关联
  benefit Benefit @relation(fields: [benefitCode], references: [code])

  @@map("t_product_benefit_rule")
}

// 用户权益表 (原 UserSubscription)
model UserBenefit {
  /// @comment 用户权益ID
  id                String   @id @default(cuid())
  /// @comment 用户ID
  userId            String   @map("user_id")
  /// @comment 产品代码
  productCode       String   @map("product_code") @db.VarChar(50)
  /// @comment 权益资源代码
  benefitCode       String   @map("benefit_code") @db.VarChar(50)
  /// @comment 交易ID
  tradeId           String?  @map("trade_id")
  /// @comment 开始时间
  startTime         DateTime @map("start_time")
  /// @comment 结束时间
  endTime           DateTime @map("end_time")
  /// @comment 可用数量
  availableQuantity Int      @default(0) @map("available_quantity")
  /// @comment 已使用数量
  usedQuantity      Int      @default(0) @map("used_quantity")
  /// @comment 额外元数据
  metadata          Json?    @map("metadata")
  /// @comment 创建时间
  createdAt         DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt         DateTime @updatedAt @map("updated_at")

  /// @comment 使用记录
  usages   UserBenefitUsage[]
  /// @comment 产品关联
  product  Product            @relation(fields: [productCode], references: [code])
  /// @comment 交易关联
  trade    Trade?             @relation(fields: [tradeId], references: [id])
  /// @comment 用户关联
  user     User               @relation(fields: [userId], references: [id])
  /// @comment 权益资源关联
  benefits Benefit            @relation(fields: [benefitCode], references: [code])

  @@index([userId])
  @@index([productCode])
  @@index([benefitCode])
  @@index([tradeId])
  @@map("t_user_benefit")
}

// 权益使用记录表 (原 SubscriptionUsage)
model UserBenefitUsage {
  /// @comment 权益使用记录ID
  id            String   @id @default(cuid())
  /// @comment 用户ID
  userId        String   @map("user_id")
  /// @comment 用户权益ID
  userBenefitId String   @map("user_benefit_id")
  /// @comment 权益资源代码
  benefitCode   String   @map("benefit_code") @db.VarChar(50)
  /// @comment 使用数量
  quantity      Int      @default(1) @map("quantity")
  /// @comment 使用天数
  days          Int      @default(0) @map("days")
  /// @comment 操作类型 (INCR-增加，DECR-减少)
  action        String   @map("action") @db.VarChar(50)
  /// @comment 使用描述
  description   String?  @map("description") @db.VarChar(200)
  /// @comment 创建时间
  createdAt     DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt     DateTime @updatedAt @map("updated_at")

  /// @comment 用户权益关联
  userBenefit UserBenefit @relation(fields: [userBenefitId], references: [id])
  /// @comment 用户关联
  user        User        @relation(fields: [userId], references: [id])
  /// @comment 权益资源关联
  benefit     Benefit     @relation(fields: [benefitCode], references: [code])

  @@index([userId])
  @@index([userBenefitId])
  @@index([benefitCode])
  @@index([action])
  @@map("t_user_benefit_usage")
}

// 数据字典表
model Dictionary {
  /// @comment 字典ID
  id        String   @id @default(cuid())
  /// @comment 字典编码
  code      String   @unique @map("code") @db.VarChar(100)
  /// @comment 字典名称
  name      String   @map("name") @db.VarChar(100)
  /// @comment 字典类型
  type      String   @map("type") @db.VarChar(50)
  /// @comment 父级ID
  parentId  String?  @map("parent_id")
  /// @comment 排序号
  sortOrder Int      @default(0) @map("sort_order")
  /// @comment 层级
  level     Int      @default(1) @map("level")
  /// @comment 是否启用
  enabled   Boolean  @default(true) @map("enabled")
  /// @comment 扩展数据
  metadata  Json?    @map("metadata")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  /// @comment 父级关联
  parent   Dictionary?  @relation("DictionaryTree", fields: [parentId], references: [id])
  /// @comment 子级列表
  children Dictionary[] @relation("DictionaryTree")

  @@index([code])
  @@index([type])
  @@index([parentId])
  @@map("t_dictionary")
}

// 关键词表
model TrendKeyword {
  /// @comment 关键词ID
  id                    String             @id @default(cuid()) @map("id")
  /// @comment 关键词内容
  word                  String             @unique @map("word")
  /// @comment 类型（root/related）
  type                  String             @map("type")
  /// @comment 父词ID（词根为null，关联词指向词根id）
  parentId              String?            @map("parent_id")
  /// @comment 来源（google_trends/手动添加）
  source                String             @map("source")
  /// @comment 是否全员可见（管理员添加）
  isGlobal              Boolean            @default(false) @map("is_global")
  /// @comment 关联用户关系表（一个关键词可被多个用户加为私有词根）
  userRelations         TrendUserKeyword[]
  /// @comment 关键词收藏
  favorites             TrendFavorite[]
  /// @comment 词根-关联词关系
  parent                TrendKeyword?      @relation("TrendKeywordTree", fields: [parentId], references: [id])
  children              TrendKeyword[]     @relation("TrendKeywordTree")
  /// @comment 其它业务字段
  updateInterval        Int                @default(1440) @map("update_interval")
  status                String             @map("status")
  trendScore            Json?              @map("trend_score")
  trendData             Json?              @map("trend_data")
  trendLastUpdate       DateTime?          @map("trend_last_update")
  aiScore               Float?             @map("ai_score")
  aiTags                Json?              @map("ai_tags")
  aiStatus              String             @map("ai_status")
  aiLastUpdate          DateTime?          @map("ai_last_update")
  isTodayUpdated        Boolean            @default(false) @map("is_today_updated")
  createdAt             DateTime           @default(now()) @map("created_at")
  updatedAt             DateTime           @updatedAt @map("updated_at")
  lastRelatedRefreshAt  DateTime?          @map("last_related_refresh_at")
  relatedUpdateInterval Int?               @default(300) @map("related_update_interval")
  relatedStatus         String             @map("related_status")

  @@index([type])
  @@index([parentId])
  @@map("t_trend_keyword")
}

// 用户和关键词的关联表，支持一个关键词被多个用户加为私有词根
model TrendUserKeyword {
  /// @comment 关联ID
  id          String    @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId      String    @map("user_id")
  /// @comment 关键词ID
  keywordId   String    @map("keyword_id")
  /// @comment 关键词类型（root/related）
  keywordType String    @default("ROOT") @map("keyword_type")
  /// @comment 创建时间
  createdAt   DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime? @updatedAt @map("updated_at")

  /// @comment 关联用户
  user    User         @relation(fields: [userId], references: [id])
  /// @comment 关联关键词
  keyword TrendKeyword @relation(fields: [keywordId], references: [id])

  @@index([userId])
  @@index([keywordId])
  @@map("t_trend_user_keyword")
}

// 用户收藏关键词
model TrendFavorite {
  id        String   @id @default(cuid()) @map("id")
  userId    String   @map("user_id")
  keywordId String   @map("keyword_id")
  createdAt DateTime @default(now()) @map("created_at")

  user    User         @relation(fields: [userId], references: [id])
  keyword TrendKeyword @relation(fields: [keywordId], references: [id])

  @@index([userId])
  @@index([keywordId])
  @@map("t_trend_favorite")
}

// 关键词查询日志
model TrendAPIQueryLog {
  /// @comment 日志ID
  id         String   @id @default(cuid()) @map("id")
  /// @comment 关键词ID
  keywordId  String   @map("keyword_id")
  /// @comment 查询时间
  queryTime  DateTime @default(now()) @map("query_time")
  /// @comment 查询类型（趋势/AI判定）
  queryType  String   @map("query_type")
  /// @comment 状态（成功/失败）
  status     String   @map("status")
  /// @comment 错误信息
  errorMsg   String?  @map("error_msg")
  /// @comment 响应内容（JSON）
  response   Json?    @map("response")
  /// @comment 用时（ms）
  durationMs Int?     @map("duration_ms")
  /// @comment 创建时间
  createdAt  DateTime @default(now()) @map("created_at")

  @@map("t_trend_api_query_log")
}

// 关键词任务日志
model TrendTaskLog {
  /// @comment 任务日志ID
  id          String    @id @default(cuid()) @map("id")
  /// @comment 关键词ID
  keywordId   String    @map("keyword_id")
  /// @comment 任务类型（趋势/AI）
  taskType    String    @map("task_type")
  /// @comment 触发方式（定时/手动）
  triggerType String    @map("trigger_type")
  /// @comment 状态（待执行/执行中/成功/失败）
  status      String    @map("status")
  /// @comment 开始时间
  startTime   DateTime  @map("start_time")
  /// @comment 结束时间
  endTime     DateTime? @map("end_time")
  /// @comment 错误信息
  errorMsg    String?   @map("error_msg")
  /// @comment 响应内容
  response    Json?     @map("response")
  /// @comment 操作人ID（手动触发时记录管理员id）
  operatorId  String?   @map("operator_id")
  /// @comment 创建时间
  createdAt   DateTime  @default(now()) @map("created_at")

  @@map("t_trend_task_log")
}


model ProjectSiteSetting {
  /// @comment 设置ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId   String   @map("project_id")
  /// @comment 语言代码
  locale      String   @map("locale") @db.VarChar(10)
  /// @comment 是否为主要配置
  isPrimary   Boolean  @default(false) @map("is_primary")
  /// @comment 网站名称
  siteName    String?  @map("site_name") @db.VarChar(100)
  /// @comment 网站Logo
  logo        String?  @map("logo") @db.VarChar(255)
  /// @comment 暗色Logo
  darkLogo    String?  @map("dark_logo") @db.VarChar(255)
  /// @comment 网站图标
  favicon     String?  @map("favicon") @db.VarChar(255)
  /// @comment 联系邮箱
  contactEmail String? @map("contact_email") @db.VarChar(100)
  /// @comment 域名
  domain      String?  @map("domain") @db.VarChar(100)
  /// @comment 分析代码配置
  analytics   Json?    @map("analytics") // 包含 gaId, clarityId, plausible 等
  /// @comment 社交链接
  socialLinks Json?    @map("social_links") // 包含 twitter, facebook, instagram 等
  /// @comment 头部配置
  headerConfig Json?   @map("header_config") // 包含 showSearch, showLanguageSelector 等
  /// @comment 底部配置
  footerConfig Json?   @map("footer_config") // 包含 showCopyright, copyrightText 等
  /// @comment 字体配置
  fonts       Json?    @map("fonts") // 包含 sans, serif, mono 等
  /// @comment 主题配置
  theme       String?  @map("theme") @db.VarChar(50)
  /// @comment 菜单项
  menuItems   Json?    @map("menu_items") // 存储菜单结构
  /// @comment 自定义头部内容
  customHeaderContent String? @map("custom_header_content") @db.Text
  /// @comment ads.txt 内容
  adsTxtContent String? @map("ads_txt_content") @db.Text
  /// @comment 图标和图片配置
  icons       Json?    @map("icons") // 包含 favicon, appleTouchIcon, androidIcon 等
  /// @comment OpenGraph 信息
  openGraph   Json?    @map("open_graph") // 包含 title, description, image 等
  /// @comment 元数据
  metadata    Json?    @map("metadata") // 存储其他元数据
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, locale, isPrimary])
  @@index([projectId])
  @@index([locale])
  @@map("p_project_site_setting")
}