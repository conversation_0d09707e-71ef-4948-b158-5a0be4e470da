// 后台任务模型

// 后台任务表
model BackgroundTask {
  /// @comment 任务ID
  id            String   @id @default(cuid()) @map("id")
  /// @comment 任务类型 (TRANSLATION等)
  type          String   @map("type") @db.VarChar(50)
  /// @comment 任务状态 (PENDING, PROCESSING, SUCCESS, FAILED_QUEUED, FAILED)
  status        String   @map("status") @db.VarChar(50)
  /// @comment 项目ID
  projectId     String   @map("project_id")
  /// @comment 用户ID
  userId        String   @map("user_id")
  /// @comment 任务标题
  title         String   @map("title") @db.VarChar(200)
  /// @comment 任务描述
  description   String?  @map("description") @db.Text
  /// @comment 任务参数 (JSON格式存储具体的任务参数)
  parameters    Json     @map("parameters")
  /// @comment 任务结果 (JSON格式存储任务执行结果)
  result        Json?    @map("result")
  /// @comment 错误信息
  errorMessage  String?  @map("error_message") @db.Text
  /// @comment 重试次数
  retryCount    Int      @default(0) @map("retry_count")
  /// @comment 最大重试次数
  maxRetries    Int      @default(3) @map("max_retries")
  /// @comment 消息队列ID
  messageId     String?  @map("message_id")
  /// @comment 开始时间
  startedAt     DateTime? @map("started_at")
  /// @comment 完成时间
  completedAt   DateTime? @map("completed_at")
  /// @comment 创建时间
  createdAt     DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联
  project       Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@map("t_background_task")
}
