// 提示词模板模型

// 提示词模板表
model PromptTemplate {
  /// @comment 提示词模板ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 项目ID (null表示系统级提示词)
  projectId   String?  @map("project_id")
  /// @comment 提示词类型 (TRANSLATION_GAME_CONTENT, TRANSLATION_ARTICLE, GAME_ARTICLE_GENERATION, GAME_COPY_GENERATION)
  type        String   @map("type") @db.VarChar(100)
  /// @comment 提示词名称
  name        String   @map("name") @db.VarChar(200)
  /// @comment 提示词描述
  description String?  @map("description") @db.Text
  /// @comment 提示词内容
  content     String   @map("content") @db.Text
  /// @comment 是否启用
  isEnabled   Boolean  @default(true) @map("is_enabled")
  /// @comment 是否为系统默认提示词
  isDefault   Boolean  @default(false) @map("is_default")
  /// @comment 创建者ID
  createdBy   String   @map("created_by")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  project     Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator     User     @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  testRecords PromptTestRecord[]

  @@unique([projectId, type], name: "project_prompt_type_unique")
  @@index([projectId])
  @@index([type])
  @@index([isEnabled])
  @@index([isDefault])
  @@index([createdBy])
  @@map("t_prompt_template")
}

// 提示词测试记录表
model PromptTestRecord {
  /// @comment 测试记录ID
  id               String   @id @default(cuid()) @map("id")
  /// @comment 提示词模板ID
  promptTemplateId String   @map("prompt_template_id")
  /// @comment 项目ID
  projectId        String   @map("project_id")
  /// @comment 用户ID
  userId           String   @map("user_id")
  /// @comment 测试输入参数
  inputParams      Json     @map("input_params")
  /// @comment 测试输出结果
  outputResult     Json?    @map("output_result")
  /// @comment 测试状态 (PENDING, SUCCESS, FAILED)
  status           String   @default("PENDING") @map("status") @db.VarChar(50)
  /// @comment 错误信息
  errorMessage     String?  @map("error_message") @db.Text
  /// @comment 耗时(毫秒)
  duration         Int?     @map("duration")
  /// @comment 创建时间
  createdAt        DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联
  promptTemplate PromptTemplate @relation(fields: [promptTemplateId], references: [id], onDelete: Cascade)
  project        Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([promptTemplateId])
  @@index([projectId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@map("t_prompt_test_record")
}

// 项目提示词使用限制表
model ProjectPromptUsageLimit {
  /// @comment 记录ID
  id            String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId     String   @map("project_id")
  /// @comment 日期 (YYYY-MM-DD)
  date          String   @map("date") @db.VarChar(10)
  /// @comment 已使用测试次数
  usedTestCount Int      @default(0) @map("used_test_count")
  /// @comment 最大测试次数限制
  maxTestCount  Int      @default(20) @map("max_test_count")
  /// @comment 创建时间
  createdAt     DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, date], name: "project_date_unique")
  @@index([projectId])
  @@index([date])
  @@map("t_project_prompt_usage_limit")
}
