-- CreateTable
CREATE TABLE "t_prompt_template" (
    "id" TEXT NOT NULL,
    "project_id" TEXT,
    "type" VARCHAR(100) NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "description" TEXT,
    "content" TEXT NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "t_prompt_template_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "t_prompt_test_record" (
    "id" TEXT NOT NULL,
    "prompt_template_id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "input_params" JSONB NOT NULL,
    "output_result" JSONB,
    "status" VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    "error_message" TEXT,
    "duration" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "t_prompt_test_record_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "t_project_prompt_usage_limit" (
    "id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "date" VARCHAR(10) NOT NULL,
    "used_test_count" INTEGER NOT NULL DEFAULT 0,
    "max_test_count" INTEGER NOT NULL DEFAULT 20,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "t_project_prompt_usage_limit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "t_prompt_template_project_id_idx" ON "t_prompt_template"("project_id");

-- CreateIndex
CREATE INDEX "t_prompt_template_type_idx" ON "t_prompt_template"("type");

-- CreateIndex
CREATE INDEX "t_prompt_template_is_enabled_idx" ON "t_prompt_template"("is_enabled");

-- CreateIndex
CREATE INDEX "t_prompt_template_is_default_idx" ON "t_prompt_template"("is_default");

-- CreateIndex
CREATE INDEX "t_prompt_template_created_by_idx" ON "t_prompt_template"("created_by");

-- CreateIndex
CREATE UNIQUE INDEX "t_prompt_template_project_id_type_key" ON "t_prompt_template"("project_id", "type");

-- CreateIndex
CREATE INDEX "t_prompt_test_record_prompt_template_id_idx" ON "t_prompt_test_record"("prompt_template_id");

-- CreateIndex
CREATE INDEX "t_prompt_test_record_project_id_idx" ON "t_prompt_test_record"("project_id");

-- CreateIndex
CREATE INDEX "t_prompt_test_record_user_id_idx" ON "t_prompt_test_record"("user_id");

-- CreateIndex
CREATE INDEX "t_prompt_test_record_status_idx" ON "t_prompt_test_record"("status");

-- CreateIndex
CREATE INDEX "t_prompt_test_record_created_at_idx" ON "t_prompt_test_record"("created_at");

-- CreateIndex
CREATE INDEX "t_project_prompt_usage_limit_project_id_idx" ON "t_project_prompt_usage_limit"("project_id");

-- CreateIndex
CREATE INDEX "t_project_prompt_usage_limit_date_idx" ON "t_project_prompt_usage_limit"("date");

-- CreateIndex
CREATE UNIQUE INDEX "t_project_prompt_usage_limit_project_id_date_key" ON "t_project_prompt_usage_limit"("project_id", "date");

-- AddForeignKey
ALTER TABLE "t_prompt_template" ADD CONSTRAINT "t_prompt_template_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "t_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_prompt_template" ADD CONSTRAINT "t_prompt_template_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "t_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_prompt_test_record" ADD CONSTRAINT "t_prompt_test_record_prompt_template_id_fkey" FOREIGN KEY ("prompt_template_id") REFERENCES "t_prompt_template"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_prompt_test_record" ADD CONSTRAINT "t_prompt_test_record_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "t_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_prompt_test_record" ADD CONSTRAINT "t_prompt_test_record_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "t_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_project_prompt_usage_limit" ADD CONSTRAINT "t_project_prompt_usage_limit_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "t_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;
