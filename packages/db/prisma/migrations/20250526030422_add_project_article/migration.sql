-- CreateTable
CREATE TABLE "p_project_article" (
    "id" TEXT NOT NULL,
    "project_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "mdx_content" TEXT NOT NULL,
    "locale" VARCHAR(10) NOT NULL,
    "title_image_url" TEXT,
    "author" TEXT,
    "author_image_url" TEXT,
    "read_time" TEXT,
    "tags" JSONB,
    "category_code" TEXT,
    "metadata" JSONB NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "p_project_article_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "p_project_article_project_id_idx" ON "p_project_article"("project_id");

-- CreateIndex
CREATE INDEX "p_project_article_user_id_idx" ON "p_project_article"("user_id");

-- CreateIndex
CREATE INDEX "p_project_article_locale_idx" ON "p_project_article"("locale");

-- CreateIndex
CREATE INDEX "p_project_article_status_idx" ON "p_project_article"("status");

-- CreateIndex
CREATE INDEX "p_project_article_category_code_idx" ON "p_project_article"("category_code");

-- CreateIndex
CREATE UNIQUE INDEX "p_project_article_project_id_slug_locale_key" ON "p_project_article"("project_id", "slug", "locale");

-- AddForeignKey
ALTER TABLE "p_project_article" ADD CONSTRAINT "p_project_article_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "t_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "p_project_article" ADD CONSTRAINT "p_project_article_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "t_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
