-- CreateTable
CREATE TABLE "t_background_task" (
    "id" TEXT NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "status" VARCHAR(50) NOT NULL,
    "project_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "description" TEXT,
    "parameters" JSONB NOT NULL,
    "result" JSONB,
    "error_message" TEXT,
    "retry_count" INTEGER NOT NULL DEFAULT 0,
    "max_retries" INTEGER NOT NULL DEFAULT 3,
    "message_id" TEXT,
    "started_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "t_background_task_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "t_background_task_project_id_idx" ON "t_background_task"("project_id");

-- CreateIndex
CREATE INDEX "t_background_task_user_id_idx" ON "t_background_task"("user_id");

-- CreateIndex
CREATE INDEX "t_background_task_type_idx" ON "t_background_task"("type");

-- CreateIndex
CREATE INDEX "t_background_task_status_idx" ON "t_background_task"("status");

-- CreateIndex
CREATE INDEX "t_background_task_created_at_idx" ON "t_background_task"("created_at");

-- AddForeignKey
ALTER TABLE "t_background_task" ADD CONSTRAINT "t_background_task_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "t_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "t_background_task" ADD CONSTRAINT "t_background_task_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "t_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
