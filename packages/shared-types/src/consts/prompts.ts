// 提示词模板类型常量
export const PROMPT_TEMPLATE_TYPE = {
  // 翻译类提示词
  TRANSLATION_GAME_CONTENT: 'TRANSLATION_GAME_CONTENT',
  TRANSLATION_ARTICLE: 'TRANSLATION_ARTICLE',
  TRANSLATION_METADATA: 'TRANSLATION_METADATA',
  TRANSLATION_SITE_SETTING: 'TRANSLATION_SITE_SETTING',
  
  // 生成类提示词
  GAME_ARTICLE_GENERATION: 'GAME_ARTICLE_GENERATION',
  GAME_COPY_GENERATION: 'GAME_COPY_GENERATION',
  GAME_FAQ_GENERATION: 'GAME_FAQ_GENERATION',
  GAME_COMMENT_GENERATION: 'GAME_COMMENT_GENERATION',
} as const

// 提示词模板类型
export type PromptTemplateType = typeof PROMPT_TEMPLATE_TYPE[keyof typeof PROMPT_TEMPLATE_TYPE]

// 提示词模板状态常量
export const PROMPT_TEMPLATE_STATUS = {
  // 启用
  ENABLED: 'ENABLED',
  // 禁用
  DISABLED: 'DISABLED',
} as const

// 提示词模板状态类型
export type PromptTemplateStatus = typeof PROMPT_TEMPLATE_STATUS[keyof typeof PROMPT_TEMPLATE_STATUS]

// 提示词测试状态常量
export const PROMPT_TEST_STATUS = {
  // 待处理
  PENDING: 'PENDING',
  // 处理中
  PROCESSING: 'PROCESSING',
  // 成功
  SUCCESS: 'SUCCESS',
  // 失败
  FAILED: 'FAILED',
} as const

// 提示词测试状态类型
export type PromptTestStatus = typeof PROMPT_TEST_STATUS[keyof typeof PROMPT_TEST_STATUS]

// 提示词类型标签映射
export const PROMPT_TYPE_LABELS = {
  [PROMPT_TEMPLATE_TYPE.TRANSLATION_GAME_CONTENT]: '游戏内容翻译',
  [PROMPT_TEMPLATE_TYPE.TRANSLATION_ARTICLE]: '文章翻译',
  [PROMPT_TEMPLATE_TYPE.TRANSLATION_METADATA]: '元数据翻译',
  [PROMPT_TEMPLATE_TYPE.TRANSLATION_SITE_SETTING]: '站点设置翻译',
  [PROMPT_TEMPLATE_TYPE.GAME_ARTICLE_GENERATION]: '游戏文章生成',
  [PROMPT_TEMPLATE_TYPE.GAME_COPY_GENERATION]: '游戏文案生成',
  [PROMPT_TEMPLATE_TYPE.GAME_FAQ_GENERATION]: '游戏FAQ生成',
  [PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION]: '游戏评论生成',
} as const

// 默认提示词内容
export const DEFAULT_PROMPT_CONTENTS = {
  [PROMPT_TEMPLATE_TYPE.TRANSLATION_GAME_CONTENT]: `你是一个专业的游戏内容翻译专家。请将以下游戏内容从{sourceLanguage}翻译为{targetLanguage}。

要求：
1. 保持游戏术语的准确性和一致性
2. 保持原文的语气和风格
3. 确保翻译自然流畅
4. 保持格式不变

原文内容：
{content}

请直接返回翻译结果，不要添加任何解释。`,

  [PROMPT_TEMPLATE_TYPE.TRANSLATION_ARTICLE]: `你是一个专业的文章翻译专家。请将以下文章内容从{sourceLanguage}翻译为{targetLanguage}。

要求：
1. 保持文章的专业性和可读性
2. 保持原文的结构和格式
3. 确保翻译准确且自然
4. 保持技术术语的准确性

原文内容：
{content}

请直接返回翻译结果，不要添加任何解释。`,

  [PROMPT_TEMPLATE_TYPE.TRANSLATION_METADATA]: `你是一个专业的SEO元数据翻译专家。请将以下元数据从{sourceLanguage}翻译为{targetLanguage}。

要求：
1. 保持SEO友好性
2. 确保标题和描述的吸引力
3. 保持关键词的相关性
4. 符合目标语言的表达习惯

原文内容：
{content}

请直接返回翻译结果，不要添加任何解释。`,

  [PROMPT_TEMPLATE_TYPE.TRANSLATION_SITE_SETTING]: `你是一个专业的网站界面翻译专家。请将以下网站设置内容从{sourceLanguage}翻译为{targetLanguage}。

要求：
1. 保持界面文本的简洁性
2. 确保用户体验友好
3. 保持功能性文本的准确性
4. 符合目标语言的使用习惯

原文内容：
{content}

请直接返回翻译结果，不要添加任何解释。`,

  [PROMPT_TEMPLATE_TYPE.GAME_ARTICLE_GENERATION]: `你是一个专业的游戏内容创作专家。请根据以下游戏信息生成一篇高质量的游戏文章。

游戏信息：
{gameInfo}

要求：
1. 文章长度：{wordCount}字左右
2. 语言：{language}
3. 包含游戏玩法、特色、评价等内容
4. 语言生动有趣，吸引读者
5. 结构清晰，段落分明

请生成文章内容：`,

  [PROMPT_TEMPLATE_TYPE.GAME_COPY_GENERATION]: `你是一个专业的游戏营销文案专家。请根据以下游戏信息生成吸引人的游戏文案。

游戏信息：
{gameInfo}

要求：
1. 文案类型：{copyType}
2. 语言：{language}
3. 突出游戏亮点和特色
4. 语言简洁有力，具有感染力
5. 符合目标受众的喜好

请生成文案内容：`,

  [PROMPT_TEMPLATE_TYPE.GAME_FAQ_GENERATION]: `你是一个专业的游戏客服专家。请根据以下游戏信息生成常见问题解答。

游戏信息：
{gameInfo}

要求：
1. 生成{faqCount}个常见问题
2. 语言：{language}
3. 问题要贴近玩家实际需求
4. 答案要详细且易懂
5. 覆盖游戏玩法、技巧、问题解决等方面

请生成FAQ内容：`,

  [PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION]: `你是一个游戏玩家。请根据以下游戏信息生成真实的游戏评论。

游戏信息：
{gameInfo}

要求：
1. 生成{commentCount}条评论
2. 语言：{language}
3. 评论要真实自然，像真实玩家写的
4. 包含不同角度的评价
5. 语言风格多样化，有积极有中性

请生成评论内容：`,
} as const

// 每日测试限制
export const DAILY_TEST_LIMIT = 20
