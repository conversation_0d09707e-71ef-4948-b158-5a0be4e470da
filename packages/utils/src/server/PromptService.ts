import { prisma } from "@repo/db"
import { getLogger } from "@repo/logger"
import { 
  PROMPT_TEMPLATE_TYPE, 
  DEFAULT_PROMPT_CONTENTS,
  type PromptTemplateType 
} from "@repo/shared-types"

const logger = getLogger("PromptService")

/**
 * 提示词服务
 * 用于获取项目级或系统级的提示词模板
 */
export class PromptService {
  /**
   * 获取提示词内容
   * 优先级：项目级提示词 > 系统级提示词 > 默认提示词
   * 
   * @param type 提示词类型
   * @param projectId 项目ID（可选）
   * @returns 提示词内容
   */
  static async getPromptContent(
    type: PromptTemplateType, 
    projectId?: string
  ): Promise<string> {
    try {
      // 1. 优先获取项目级提示词
      if (projectId) {
        const projectPrompt = await prisma.promptTemplate.findFirst({
          where: {
            projectId,
            type,
            isEnabled: true
          }
        })

        if (projectPrompt) {
          logger.debug(`使用项目级提示词: ${type} for project ${projectId}`)
          return projectPrompt.content
        }
      }

      // 2. 获取系统级提示词（projectId为null）
      const systemPrompt = await prisma.promptTemplate.findFirst({
        where: {
          projectId: null,
          type,
          isEnabled: true,
          isDefault: true
        }
      })

      if (systemPrompt) {
        logger.debug(`使用系统级提示词: ${type}`)
        return systemPrompt.content
      }

      // 3. 使用默认提示词
      const defaultContent = DEFAULT_PROMPT_CONTENTS[type]
      if (defaultContent) {
        logger.debug(`使用默认提示词: ${type}`)
        return defaultContent
      }

      // 4. 如果都没有，抛出错误
      throw new Error(`未找到提示词模板: ${type}`)
    } catch (error) {
      logger.error(`获取提示词失败: ${type}`, error)
      
      // 降级到默认提示词
      const defaultContent = DEFAULT_PROMPT_CONTENTS[type]
      if (defaultContent) {
        logger.warn(`降级使用默认提示词: ${type}`)
        return defaultContent
      }
      
      throw error
    }
  }

  /**
   * 获取翻译提示词
   * 
   * @param contentType 内容类型
   * @param projectId 项目ID
   * @returns 翻译提示词内容
   */
  static async getTranslationPrompt(
    contentType: string, 
    projectId?: string
  ): Promise<string> {
    // 根据内容类型映射到提示词类型
    const promptTypeMap: Record<string, PromptTemplateType> = {
      'ProjectGameLocale_metadata': PROMPT_TEMPLATE_TYPE.TRANSLATION_GAME_CONTENT,
      'ProjectGameLocale_content': PROMPT_TEMPLATE_TYPE.TRANSLATION_GAME_CONTENT,
      'Metadata': PROMPT_TEMPLATE_TYPE.TRANSLATION_METADATA,
      'Article': PROMPT_TEMPLATE_TYPE.TRANSLATION_ARTICLE,
      'SiteSetting': PROMPT_TEMPLATE_TYPE.TRANSLATION_SITE_SETTING,
    }

    const promptType = promptTypeMap[contentType] || PROMPT_TEMPLATE_TYPE.TRANSLATION_GAME_CONTENT
    return await this.getPromptContent(promptType, projectId)
  }

  /**
   * 替换提示词中的变量
   * 
   * @param template 提示词模板
   * @param variables 变量对象
   * @returns 替换后的提示词
   */
  static replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template
    
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{${key}}`
      result = result.replace(new RegExp(placeholder, 'g'), String(value))
    }
    
    return result
  }

  /**
   * 构建翻译提示词
   * 
   * @param contentType 内容类型
   * @param sourceLanguage 源语言
   * @param targetLanguage 目标语言
   * @param content 要翻译的内容
   * @param projectId 项目ID
   * @returns 完整的翻译提示词
   */
  static async buildTranslationPrompt(
    contentType: string,
    sourceLanguage: string,
    targetLanguage: string,
    content: string,
    projectId?: string
  ): Promise<string> {
    const template = await this.getTranslationPrompt(contentType, projectId)
    
    return this.replaceVariables(template, {
      sourceLanguage,
      targetLanguage,
      content: typeof content === 'string' ? content : JSON.stringify(content, null, 2)
    })
  }

  /**
   * 获取生成类提示词
   * 
   * @param type 生成类型
   * @param params 生成参数
   * @param projectId 项目ID
   * @returns 完整的生成提示词
   */
  static async buildGenerationPrompt(
    type: 'article' | 'copy' | 'faq' | 'comment',
    params: Record<string, any>,
    projectId?: string
  ): Promise<string> {
    const promptTypeMap = {
      'article': PROMPT_TEMPLATE_TYPE.GAME_ARTICLE_GENERATION,
      'copy': PROMPT_TEMPLATE_TYPE.GAME_COPY_GENERATION,
      'faq': PROMPT_TEMPLATE_TYPE.GAME_FAQ_GENERATION,
      'comment': PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION,
    }

    const promptType = promptTypeMap[type]
    const template = await this.getPromptContent(promptType, projectId)
    
    return this.replaceVariables(template, params)
  }

  /**
   * 批量获取项目的所有提示词
   * 
   * @param projectId 项目ID
   * @returns 提示词映射对象
   */
  static async getProjectPrompts(projectId: string): Promise<Record<string, string>> {
    try {
      const prompts = await prisma.promptTemplate.findMany({
        where: {
          projectId,
          isEnabled: true
        }
      })

      const promptMap: Record<string, string> = {}
      for (const prompt of prompts) {
        promptMap[prompt.type] = prompt.content
      }

      return promptMap
    } catch (error) {
      logger.error(`获取项目提示词失败: ${projectId}`, error)
      return {}
    }
  }

  /**
   * 初始化系统默认提示词
   * 在系统启动时调用，确保系统级默认提示词存在
   */
  static async initializeSystemPrompts(): Promise<void> {
    try {
      logger.info("初始化系统默认提示词...")

      for (const [type, content] of Object.entries(DEFAULT_PROMPT_CONTENTS)) {
        const existingPrompt = await prisma.promptTemplate.findFirst({
          where: {
            projectId: null,
            type,
            isDefault: true
          }
        })

        if (!existingPrompt) {
          await prisma.promptTemplate.create({
            data: {
              projectId: null,
              type,
              name: `系统默认 - ${type}`,
              description: `系统默认的${type}提示词模板`,
              content,
              isEnabled: true,
              isDefault: true,
              createdBy: 'system' // 系统创建的提示词
            }
          })
          logger.info(`创建系统默认提示词: ${type}`)
        }
      }

      logger.info("系统默认提示词初始化完成")
    } catch (error) {
      logger.error("初始化系统默认提示词失败:", error)
      throw error
    }
  }
}

// 导出便捷函数
export const getPromptContent = PromptService.getPromptContent
export const getTranslationPrompt = PromptService.getTranslationPrompt
export const buildTranslationPrompt = PromptService.buildTranslationPrompt
export const buildGenerationPrompt = PromptService.buildGenerationPrompt
export const replaceVariables = PromptService.replaceVariables
