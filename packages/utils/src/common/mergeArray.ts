/**
 * 将 news 中的同下标和同名属性覆盖到 tree 中并返回合并修改后的 tree
 * @param tree 原始数组
 * @param news 新数组，用于覆盖原始数组中的值
 * @param getKey 可选的查找函数，用于确定如何匹配对象。如果提供，则使用该函数返回的键进行匹配；否则使用数组下标匹配
 * @returns 合并后的数组
 */
export function mergeArray<T extends Record<string, any>>(
  tree: T[],
  news: T[],
  getKey?: ((item: T) => string | number) | null
): T[] {
  // 如果 tree 为空，直接返回 news
  if (!tree || tree.length === 0) {
    return [...news]
  }

  // 如果 news 为空，直接返回 tree
  if (!news || news.length === 0) {
    return [...tree]
  }

  // 创建 tree 的副本，避免修改原始数组
  const result = JSON.parse(JSON.stringify(tree)) as T[]

  // 如果提供了 getKey 函数，使用键值匹配
  if (getKey) {
    // 创建 tree 中对象的键值映射
    const treeMap = new Map<string | number, number>()
    result.forEach((item, index) => {
      const key = getKey(item)
      treeMap.set(key, index)
    })

    // 遍历 news 数组
    news.forEach((newItem) => {
      const key = getKey(newItem)
      const treeIndex = treeMap.get(key)

      // 如果在 tree 中找到匹配项，则合并属性
      if (treeIndex !== undefined) {
        // 合并对象属性
        // @ts-ignore
        result[treeIndex] = mergeObjects(result[treeIndex], newItem, getKey)
      } else {
        // 如果在 tree 中没有找到匹配项，则添加到结果中
        result.push(newItem)
      }
    })
  } else {
    // 如果没有提供 getKey 函数，使用数组下标匹配
    news.forEach((newItem, index) => {
      if (index < result.length) {
        // 合并对象属性
        // @ts-ignore
        result[index] = mergeObjects(result[index], newItem)
      } else {
        // 如果 news 数组比 tree 长，则添加额外的项
        result.push(newItem)
      }
    })
  }

  return result
}

/**
 * 递归合并两个对象的属性
 * @param target 目标对象
 * @param source 源对象，其属性将覆盖目标对象
 * @param getKey 可选的查找函数，用于确定如何匹配对象
 * @returns 合并后的对象
 */
function mergeObjects<T extends Record<string, any>>(
  target: T,
  source: T,
  getKey?: ((item: any) => string | number) | null
): T {
  const result = {...target}

  // 遍历源对象的所有属性
  Object.keys(source).forEach((key) => {
    const sourceValue = source[key]
    const targetValue = target[key]

    // 如果源值是对象且不是数组，且目标值也是对象且不是数组，则递归合并
    if (
      sourceValue &&
      typeof sourceValue === "object" &&
      !Array.isArray(sourceValue) &&
      targetValue &&
      typeof targetValue === "object" &&
      !Array.isArray(targetValue)
    ) {
      // @ts-ignore
      result[key] = mergeObjects(targetValue, sourceValue, getKey)
    }
    // 如果源值是数组且目标值也是数组，则递归合并数组
    else if (
      Array.isArray(sourceValue) &&
      Array.isArray(targetValue)
    ) {
      // 如果数组元素是对象且提供了 getKey 函数，则使用 getKey 函数匹配数组元素
      if (getKey && sourceValue.length > 0 && typeof sourceValue[0] === 'object') {
        // 为数组元素创建一个新的 getKey 函数，用于匹配嵌套数组中的元素
        const arrayItemGetKey = (item: Record<string, any>) => {
          try {
            // 尝试使用原始 getKey 函数
            if (getKey) {
              return getKey(item)
            }
          } catch (error) {
            // 如果出错，则继续尝试其他方法
          }

          // 如果 getKey 函数不适用于数组元素，则尝试使用常见的 ID 字段
          if (item.id !== undefined) return item.id
          if (item.key !== undefined) return item.key
          if (item.code !== undefined) return item.code
          // 如果没有可用的键，则返回 null，这将导致使用索引匹配
          return null
        }

        // 使用类型断言确保类型安全
        // @ts-ignore
        result[key] = mergeArray(targetValue as Record<string, any>[], sourceValue as Record<string, any>[], arrayItemGetKey)
      } else {
        // 如果没有提供 getKey 函数或数组元素不是对象，则使用索引匹配
        // @ts-ignore
        result[key] = mergeArray(targetValue as any[], sourceValue as any[])
      }
    }
    // 否则，直接覆盖
    else {
      // @ts-ignore
      result[key] = sourceValue
    }
  })

  return result
}
