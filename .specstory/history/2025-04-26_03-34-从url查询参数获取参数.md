<!-- Generated by SpecStory -->

# 从URL查询参数获取参数 (2025-04-26 11:34:53)

_**User**_

改为从url的?当中获取参数

---

_**Assistant**_

我来帮你修改代码，将参数获取方式从 URL 路径参数改为查询参数。我会使用 `useSearchParams` 来获取 URL 查询参数。

让我先修改这个文件：


---

```diff
  import { useEffect, useState } from "react"
  import { useTranslations } from "next-intl"
- import { useParams } from "next/navigation"
+ import { useSearchParams } from "next/navigation"
  import { Card, Spin, Tooltip } from "antd"
  import { fetchGet } from "@repo/utils/react"
```

```diff
  export default function TrendKeywordDetailPage() {
    const t = useTranslations("trend_keyword_detail")
-   const params = useParams()
+   const searchParams = useSearchParams()
    const [loading, setLoading] = useState(true)
    const [keyword, setKeyword] = useState<TrendKeyword | null>(null)
  
    useEffect(() => {
      loadKeyword()
-   }, [params.id])
+   }, [searchParams.get('id')])
  
    const loadKeyword = async () => {
      try {
-       const res = await fetchGet(`/api/trend-keyword/${params.id}`)
+       const id = searchParams.get('id')
+       if (!id) {
+         toast.error("缺少关键词ID")
+         return
+       }
+       const res = await fetchGet(`/api/trend-keyword/${id}`)
        setKeyword(res)
      } catch (e: any) {
```

---


我已经完成了以下修改：

1. 将 `useParams` 替换为 `useSearchParams`
2. 修改了参数获取方式，从 `params.id` 改为 `searchParams.get('id')`
3. 添加了对 id 参数的空值检查
4. 更新了 `useEffect` 的依赖项

现在，这个页面将通过 URL 查询参数来获取关键词 ID，例如：`/dashboard/trend-keyword-list/details?id=123`。

主要变化：
- 使用 `useSearchParams` 替代了 `useParams`
- 添加了参数验证，确保 id 存在
- 保持了原有的错误处理和加载状态逻辑

这样的修改使得 URL 更加灵活，并且可以更容易地添加其他查询参数。需要注意的是，由于我们改变了参数获取方式，你可能需要相应地更新路由配置或链接生成方式。

---

_**User**_

趋势数据有可能为空，需要防止出错

---

_**Assistant**_

我来帮你添加对趋势数据为空的处理，以防止出现错误。



---

```diff
  
    const renderCombinedTrendChart = () => {
-     if (!keyword?.trendData) return null
+     if (!keyword?.trendData?.length) {
+       return (
+         <div className="flex justify-center items-center h-40 text-gray-500">
+           暂无趋势数据
+         </div>
+       )
+     }
  
      // 准备所有关键词的数据
      const allKeywords = [keyword, ...(keyword.children || [])]
      const datasets = allKeywords.map((kw, index) => {
+       if (!kw?.trendData?.length) return null
+       
        const colors = [
          '#8884d8', // 主词
```

```diff
          tension: 0.4,
        }
-     })
+     }).filter(Boolean) // 过滤掉无效的数据集
+ 
+     if (!datasets.length) {
+       return (
+         <div className="flex justify-center items-center h-40 text-gray-500">
+           暂无有效的趋势数据
+         </div>
+       )
+     }
  
      const chartData = {
```

```diff
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
          {keyword.children.map((child) => {
-           if (!child.trendData) return null
+           if (!child?.trendData?.length) {
+             return (
+               <Card 
+                 key={child.id} 
+                 title={
+                   <div className="flex items-center gap-2">
+                     <span>{child.word}</span>
+                     <div className="flex gap-2">
+                       <Tooltip title="Google Trends">
+                         <a 
+                           href={`https://trends.google.com/trends/explore?q=${encodeURIComponent(child.word)}`} 
+                           target="_blank" 
+                           rel="noopener noreferrer"
+                           className="text-blue-500 hover:text-blue-700"
+                         >
+                           <TrendingUp className="size-5" />
+                         </a>
+                       </Tooltip>
+                       <Tooltip title="Google Search">
+                         <a 
+                           href={`https://www.google.com/search?q=${encodeURIComponent(child.word)}`} 
+                           target="_blank" 
+                           rel="noopener noreferrer"
+                           className="text-blue-500 hover:text-blue-700"
+                         >
+                           <Search className="size-5" />
+                         </a>
+                       </Tooltip>
+                     </div>
+                   </div>
+                 }
+               >
+                 <div className="flex justify-center items-center h-40 text-gray-500">
+                   暂无趋势数据
+                 </div>
+               </Card>
+             )
+           }
  
            const chartData = {
```

---

我注意到有一个类型错误需要修复。让我来修复这个问题：



---

```diff
      // 准备所有关键词的数据
      const allKeywords = [keyword, ...(keyword.children || [])]
-     const datasets = allKeywords.map((kw, index) => {
-       if (!kw?.trendData?.length) return null
-       
+     const datasets = allKeywords
+       .filter(kw => kw?.trendData?.length) // 先过滤掉没有数据的项
+       .map((kw, index) => {
          const colors = [
            '#8884d8', // 主词
```

```diff
            tension: 0.4,
          }
-     }).filter(Boolean) // 过滤掉无效的数据集
+       })
  
      if (!datasets.length) {
```

---

我已经完成了以下改进：

1. 添加了对趋势数据为空的处理：
   - 主趋势图表显示"暂无趋势数据"的提示
   - 关联词趋势图表显示"暂无趋势数据"的提示
   - 保留了 Google Trends 和 Google Search 的链接

2. 修复了类型错误：
   - 使用 `filter` 先过滤掉没有数据的项
   - 移除了可能导致类型错误的 `null` 值
   - 确保 `datasets` 数组中的每个元素都是有效的图表数据集

3. 改进了数据验证：
   - 检查 `trendData` 是否存在且长度大于 0
   - 对每个关键词的数据进行验证
   - 添加了友好的错误提示信息

现在代码可以更好地处理以下情况：
- 主关键词没有趋势数据
- 关联词没有趋势数据
- 数据格式不正确
- 空数组或无效数据

这些改进使得页面更加健壮，能够优雅地处理各种数据异常情况。

---

_**User**_

timeframe 的值改为类似2025-04-12 2025-04-26这样的时间区间，使用当前时间往前推14天

---

_**Assistant**_

我来帮你修改代码，添加时间区间的处理。我们需要计算当前时间往前推14天的时间范围。




---

```diff
    children: TrendKeyword[]
  }
+ 
+ // 格式化日期为 YYYY-MM-DD
+ const formatDate = (date: Date) => {
+   return date.toISOString().split('T')[0]
+ }
+ 
+ // 获取默认时间范围（当前时间往前推14天）
+ const getDefaultTimeframe = () => {
+   const endDate = new Date()
+   const startDate = new Date()
+   startDate.setDate(endDate.getDate() - 14)
+   return `${formatDate(startDate)} ${formatDate(endDate)}`
+ }
  
  export default function TrendKeywordDetailPage() {
+   const t = useTranslations("trend_keyword_detail")
    const searchParams = useSearchParams()
    const [loading, setLoading] = useState(true)
    const [keyword, setKeyword] = useState<TrendKeyword | null>(null)
+   const [timeframe, setTimeframe] = useState(getDefaultTimeframe())
  
    useEffect(() => {
      loadKeyword()
-   }, [searchParams.get('id')])
+   }, [searchParams.get('id'), timeframe])
  
    const loadKeyword = async () => {
```

```diff
          return
        }
-       const res = await fetchGet(`/api/trend-keyword/${id}`)
+       const res = await fetchGet(`/api/trend-keyword/${id}?timeframe=${encodeURIComponent(timeframe)}`)
        setKeyword(res)
      } catch (e: any) {
```

---

我已经完成了以下修改：

1. 添加了日期处理工具函数：
   - `formatDate`: 将日期格式化为 YYYY-MM-DD 格式
   - `getDefaultTimeframe`: 计算默认时间范围（当前时间往前推14天）

2. 添加了时间范围状态管理：
   - 使用 `useState` 添加了 `timeframe` 状态
   - 默认值设置为当前时间往前推14天的时间范围

3. 修改了数据加载逻辑：
   - 在 API 请求中添加了 `timeframe` 参数
   - 将 `timeframe` 添加到 `useEffect` 的依赖数组中，当时间范围改变时重新加载数据

现在，每次加载数据时都会带上时间范围参数，格式为 "YYYY-MM-DD YYYY-MM-DD"。例如：
- 如果今天是 2024-04-26，默认时间

---

