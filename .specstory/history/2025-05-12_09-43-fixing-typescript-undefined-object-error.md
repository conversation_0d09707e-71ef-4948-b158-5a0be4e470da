<!-- Generated by SpecStory -->

# Fixing TypeScript Undefined Object Error (2025-05-12 17:43:56)

_**User**_

For the code present, we get this error:
```
不能调用可能是“未定义”的对象。
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

你遇到的报错是因为 `gameSettings` 实际类型是 `Record<string, string>`，它是一个对象而不是数组，不能直接用 `map` 方法。

**简明修复方法：**
将
```ts
gameSettings.map((item) => { ... })
```
改为
```ts
Object.entries(gameSettings).map(([key, value]) => (
  <>
    <span>{t(`Game.${key}`)}: {value}</span>
    <span className="hidden sm:inline">|</span>
  </>
))
```

这样可以遍历对象的每个键值对，解决类型和运行时错误。

---

