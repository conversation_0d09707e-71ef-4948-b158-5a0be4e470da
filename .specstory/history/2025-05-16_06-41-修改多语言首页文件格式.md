<!-- Generated by SpecStory -->

# 修改多语言首页文件格式 (2025-05-16 14:41:18)

_**User**_

当前项目构建完成后，多语言的首页变成了诸如：zh-TW.html 而不是 zh-TW/index.html。如何修改成我要的格式

---

_**Assistant**_



---

好的，这个问题可以通过修改 `next.config.mjs` 文件中的 `trailingSlash` 配置来解决。

当 `trailingSlash` 设置为 `false`（默认值）时，Next.js 在静态导出时会将 `/about` 这样的路径生成为 `about.html`。如果将其设置为 `true`，则会生成 `about/index.html` 这样的结构。

因此，我们只需要将 `trailingSlash: false` 修改为 `trailingSlash: true` 即可。

我将为您修改 `next.config.mjs` 文件。

---

```diff
  	reactStrictMode: true,
  	compress: true,
- 	trailingSlash: false,
+ 	trailingSlash: true,
  	output: 'export',
  	transpilePackages: ["next-mdx-remote"],
```

---

我已经修改了 `qizhilu-template-v3/next.config.mjs` 文件中的 `trailingSlash` 配置。现在，当您重新构建项目时，多语言的首页应该会生成为您期望的 `zh-TW/index.html` 格式。

请您重新构建项目并检查一下。

---

_**User**_

For the code present, we get this error:
```
不能将类型“string | string[]”分配给类型“string | undefined”。
  不能将类型“string[]”分配给类型“string”。
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_



---




---



---

您好，这个 TypeScript 错误是因为在 `qizhilu-platform/apps/web/app/api/deployment/[[...slug]]/route.ts` 文件的 `/detail` 路由处理函数中，尝试从 `request.query` 中获取 `id`，但标准的 `Request` 对象并没有 `query` 属性。此外，即使它存在并且可以返回一个字符串数组（例如 `string[]`），这也与 Prisma期望的单个 `string` 类型的 `id` 不兼容。

其他 GET 请求处理函数（如 `/list`, `/preview`, `/logs`）中正确地使用了 `new URL(request.url).searchParams.get('paramName')` 来获取查询参数。我们将采用相同的方式来修复 `/detail` 路由。

这将解决两个问题：
1. Linter 错误：`类型“RequestType”上不存在属性“query”。`
2. 类型错误：`不能将类型“string | string[]”分配给类型“string | undefined”。`

我将为您修改代码：

---

```diff
  
  	try {
- 		const { id } = request.query
+ 		const url = new URL(request.url);
+ 		const id = url.searchParams.get('id');
  
  		if (!id) {
```

---

代码已修改。新的代码使用 `new URL(request.url).searchParams.get('id')` 来获取 `id` 参数，这与文件中其他 GET 请求的处理方式一致，并且解决了您提到的类型错误。

请检查一下，看看问题是否已解决。

---

