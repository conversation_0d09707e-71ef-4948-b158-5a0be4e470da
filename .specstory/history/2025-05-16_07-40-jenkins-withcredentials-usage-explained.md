<!-- Generated by SpecStory -->

# Jenkins withCredentials Usage Explained (2025-05-16 15:40:41)

_**User**_

 // 使用withCredentials隐藏密码，防止在Jenkins日志中泄露
这个是怎么把当前密码传进去的？

---

_**Assistant**_

在 Jenkinsfile 中，`withCredentials([string(credentialsId: 'ssh-password', variable: 'HIDE_SSH_PASSWORD')])` 这段代码的作用是：

1. **从 Jenkins 的凭据管理中**，查找 ID 为 `ssh-password` 的字符串类型凭据（通常是 SSH 密码）。
2. **将该凭据的值赋给环境变量** `HIDE_SSH_PASSWORD`，在 `withCredentials` 代码块内有效。

在代码块内部，你可以直接通过 `$HIDE_SSH_PASSWORD`（Shell 脚本里）来引用这个密码。例如：

```groovy
withCredentials([string(credentialsId: 'ssh-password', variable: 'HIDE_SSH_PASSWORD')]) {
    sh '''
    sshpass -p "$HIDE_SSH_PASSWORD" ssh ...
    '''
}
```

这样做的好处是：

- 密码不会直接暴露在 Jenkins 日志中（Jenkins 会自动隐藏变量值）。
- 密码只在 `withCredentials` 代码块内有效，块外无法访问，提升安全性。

**总结**：  
Jenkins 会自动把凭据的值注入到你指定的环境变量（这里是 `HIDE_SSH_PASSWORD`）中，你在 shell 脚本里用 `$HIDE_SSH_PASSWORD` 就能拿到当前密码，无需手动传递。

---

_**User**_

我没有设置凭据，我用的是当前环境变量的SSH_PASSWORD的值，如何让它读取这个值，并且日志里面不要输出这个值

---

_**Assistant**_

你可以这样实现你的需求：

### 1. 直接用环境变量 SSH_PASSWORD

在 Jenkinsfile 里，你已经通过 `environment` 把 `SSH_PASSWORD` 设置为 `${params.ssh_password}`，所以在 shell 脚本里可以直接用 `$SSH_PASSWORD`。

### 2. 防止日志输出密码

Jenkins 默认会在日志中隐藏 `withCredentials` 注入的变量，但**不会自动隐藏普通环境变量**。  
如果你直接用 `$SSH_PASSWORD`，一旦脚本里有 `echo $SSH_PASSWORD` 或命令出错输出了密码，日志就会泄露。

#### 解决方法

- **不要在任何地方直接输出 `$SSH_PASSWORD`**（比如不要写 `echo $SSH_PASSWORD`）。
- 使用 `sshpass` 时，参数不会被日志直接输出，但如果你担心意外泄露，可以用 Jenkins 的 `maskPasswords` 插件（如果有装），或者用 shell 的技巧防止输出。

#### 推荐写法

```groovy
sh '''
# 千万不要 echo $SSH_PASSWORD
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "mkdir -p ${FTP_PATH}"
sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no ${ARCHIVE_NAME} ${SSH_USERNAME}@${SERVER_IP}:${FTP_PATH}/
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "cd ${FTP_PATH} && unzip -q -o ${ARCHIVE_NAME}"
'''
```

### 3. 进一步保护（可选）

如果你担心有些命令会把环境变量内容输出到日志，可以在 `sh` 步骤前加一行：

```groovy
// 让 Jenkins 尝试自动隐藏 SSH_PASSWORD
env.MASKED_PASSWORD = env.SSH_PASSWORD
sh '''
sshpass -p "$MASKED_PASSWORD" ...
'''
```
但最关键的还是**不要在任何地方输出密码变量**。

---

**总结：**
- 直接用 `$SSH_PASSWORD` 没问题，只要你不输出它，日志就不会泄露。
- 推荐用 `withCredentials` 是因为 Jenkins 会自动帮你隐藏日志里的敏感信息，但你不用也可以，只要小心别输出密码。
- 如果有 mask 插件可以用，也可以配置自动隐藏。

如需更高安全性，建议后续还是用 Jenkins 凭据管理。

---

_**User**_

现在这种写法就会将+ sshpass -p 'b7BxS^Dpp7' ssh -o StrictHostKeyChecking=no root@************* 'mkdir -p /www/cosfs/fafafa/game-host-2-u2fat5'
Warning: Permanently added '*************' (ED25519) to the list of known hosts.
+ sshpass -p 'b7BxS^Dpp7' scp -o StrictHostKeyChecking=no dist.zip root@*************:/www/cosfs/fafafa/game-host-2-u2fat5/
+ sshpass -p 'b7BxS^Dpp7' ssh -o StrictHostKeyChecking=no root@************* 'cd /www/cosfs/fafafa/game-host-2-u2fat5 && unzip -o dist.zip'
这样输出日志

---

_**Assistant**_

你遇到的问题是：Jenkins 日志会把 `sshpass -p 'xxx'` 这一整行原样输出，导致密码泄露。  
这是因为 Jenkins 的 `sh` 步骤会把所有执行的命令（包括参数）都打印出来。

---

## 解决方案

### 1. 使用 `set +x` 禁止命令回显

你可以在 shell 脚本前加上 `set +x`，这样不会打印命令本身（默认 Jenkins 其实是 `set -x`，会打印所有命令和参数）。

```groovy
sh '''
set +x
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "mkdir -p ${FTP_PATH}"
sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no ${ARCHIVE_NAME} ${SSH_USERNAME}@${SERVER_IP}:${FTP_PATH}/
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "cd ${FTP_PATH} && unzip -q -o ${ARCHIVE_NAME}"
'''
```

### 2. Jenkins 2.222+ 支持 `maskPasswords` 插件（推荐）

如果你有权限安装插件，可以安装 [Mask Passwords Plugin](https://plugins.jenkins.io/mask-passwords/)，它可以自动隐藏日志中的敏感变量。  
配置后，Jenkins 会自动把你指定的变量（如 `SSH_PASSWORD`）在日志中用 `****` 替换。

### 3. 用 Groovy 方式执行命令（不推荐，复杂）

用 Groovy 的 `com.cloudbees.plugins.credentials.CredentialsProvider` 方式获取密码，然后用 `sh` 执行时拼接参数，但本质还是会被日志输出。

---

## 推荐做法

**最简单安全的方式：**

```groovy
sh '''
set +x
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "mkdir -p ${FTP_PATH}"
sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no ${ARCHIVE_NAME} ${SSH_USERNAME}@${SERVER_IP}:${FTP_PATH}/
sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USERNAME}@${SERVER_IP} "cd ${FTP_PATH} && unzip -q -o ${ARCHIVE_NAME}"
set -x
'''
```

- `set +x` 关闭命令回显，防止密码泄露。
- `set -x` 恢复回显（可选）。

---

## 总结

- 只要用 `set +x`，Jenkins 日志就不会输出命令和参数，密码不会泄露。
- 最安全的还是用 Jenkins 凭据管理（`withCredentials`），Jenkins 会自动隐藏日志中的敏感信息。
- 不要在脚本中 `echo` 出密码变量。

如需更高安全性，建议后续迁移到 Jenkins 凭据管理。

---

