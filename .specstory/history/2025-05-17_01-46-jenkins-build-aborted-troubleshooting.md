<!-- Generated by SpecStory -->

# Jenkins Build Aborted Troubleshooting (2025-05-17 09:46:46)

_**User**_

ERROR: also cancelling shell steps running on 
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
Build was aborted!
[Pipeline] sh

为什么会突然中止？timeout 900 tail -f不生效？

---

_**Assistant**_

你的 Jenkins 流水线日志显示：

```
ERROR: also cancelling shell steps running on 
...
Build was aborted!
```

你怀疑 `timeout 900 tail -f` 没有生效，导致流水线提前中止。下面是详细分析：

---

### 1. `timeout 900 tail -f ${LOG_FILE}` 的作用

- 这行命令的目的是让 Jenkins 在“Show Live Logs”阶段持续输出日志 15 分钟（900 秒），防止流水线任务因无输出被 Jenkins 判定为“无活动”而自动终止。
- `timeout 900` 的作用是让 `tail -f` 最多运行 900 秒，超时后自动退出。

---

### 2. 可能的中止原因

#### 1）Jenkins 手动终止
- 如果你在 Jenkins UI 上点击了“中止”按钮，流水线会立即终止，并显示“Build was aborted!”。

#### 2）上游任务或父流水线终止
- 如果这是多阶段流水线或被其他任务触发，父任务终止会导致本任务被中止。

#### 3）Jenkins 节点资源问题
- 节点重启、断网、Jenkins master/slave 失联等，都会导致任务被中止。

#### 4）`timeout` 命令未生效
- 如果 `timeout` 命令本身不可用（比如 shell 环境没有装 coreutils），`timeout` 会报错，`tail -f` 变成无限等待，Jenkins 可能因“无输出”超时终止任务。
- 你可以在流水线前面加一行 `which timeout` 或 `timeout --version` 检查命令可用性。

#### 5）流水线全局超时
- Jenkinsfile 没有设置 `options { timeout(...) }`，但如果在 Job 配置里设置了“构建超时”，到点也会强制终止。

---

### 3. 如何排查

1. **检查 Jenkins 日志**  
   查看流水线控制台输出，是否有 `timeout` 或 `tail` 的报错信息。

2. **确认 `timeout` 命令可用**  
   在流水线脚本前加：
   ```groovy
   sh 'which timeout'
   sh 'timeout --version'
   ```

3. **确认不是手动中止**  
   检查是否有管理员或用户手动点击了“中止”。

4. **检查节点状态**  
   查看 Jenkins 节点是否有重启、掉线等异常。

5. **检查 Job 配置**  
   看是否有全局超时设置。

---

### 4. 建议

- **确认 `timeout` 可用**，否则用 `sleep 900 & tail -f ... & wait -n` 这种方式替代。
- **流水线加更多日志**，比如 `echo "Start tail"` 和 `echo "End tail"`，方便定位卡在哪一步。
- **如有必要，设置流水线全局超时**，避免无限挂起。

---

#### 结论

你的 `timeout 900 tail -f` 理论上是生效的，但如果命令不可用、节点异常、被手动中止或有全局超时，都会导致流水线提前终止。建议先排查上述几点，尤其是 `timeout` 命令的可用性和 Jenkins 节点的稳定性。

如需进一步排查，请贴出“Show Live Logs”阶段的完整控制台输出。

---

