<!-- Generated by SpecStory -->

# 统一拦截处理项目ID校验 (2025-05-15 09:42:57)

_**User**_

增加一个统一拦截处理，校验请求参数当中的projectId是否为空

---

_**Assistant**_

你希望在 API 统一网关（`gateway/[[...slug]]/route.ts`）中，增加一个统一的拦截处理，校验所有请求参数中的 `projectId` 是否为空。如果为空则直接返回错误响应。

最佳实践如下：

1. **统一拦截**：在 `router` 的中间件或全局前置处理逻辑中实现。
2. **校验方式**：支持 `GET` 的 query、`POST/PUT/DELETE` 的 body、以及 `headers`（如有）。
3. **返回格式**：保持与现有错误响应一致。

下面是实现思路：

- 在 `router` 注册全局中间件，优先于各业务路由。
- 检查 `projectId` 是否存在于 query、body 或 headers。
- 若缺失，直接返回 400 错误。

我将为你补充一段中间件代码，插入到 `route.ts` 业务路由注册之前。

请确认：
- 是否所有 API 都必须带 `projectId`？（如部分接口不需要可配置白名单）
- `projectId` 支持从 query、body、header 哪些位置获取？

如无特殊说明，将默认所有 API 都强制校验，且支持 query、body、header 三种方式。

如果确认，请回复，我将直接为你补充代码。

---

_**User**_

确认，并补充

---

_**Assistant**_

<details>
            <summary>Searched codebase "handlers" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "router" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "AutoRouter" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "itty-router" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "from 'itty-router'" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "from 'itty-router-extras'" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: qizhilu-platform/apps/web/app/api/gateway/[[...slug]]/handlers/index.ts

---

Read file: qizhilu-platform/apps/web/app/api/gateway/[[...slug]]/route.ts

---

```diff
  export const router = AutoRouter({
  	base: "/api/gateway",
- 	before: [preflight],
+ 	before: [
+ 		preflight,
+ 		async (request: Request) => {
+ 			// 1. 检查 query
+ 			const url = new URL(request.url)
+ 			const projectIdFromQuery = url.searchParams.get("projectId")
+ 
+ 			// 2. 检查 header
+ 			const projectIdFromHeader = request.headers.get("x-project-id")
+ 
+ 			// 3. 检查 body（仅 POST/PUT/DELETE）
+ 			let projectIdFromBody: string | undefined = undefined
+ 			if (["POST", "PUT", "DELETE"].includes(request.method)) {
+ 				try {
+ 					const contentType = request.headers.get("content-type") || ""
+ 					if (contentType.includes("application/json")) {
+ 						const body = await request.clone().json()
+ 						projectIdFromBody = body?.projectId
+ 					}
+ 				} catch {}
+ 			}
+ 
+ 			const projectId = projectIdFromQuery || projectIdFromHeader || projectIdFromBody
+ 			if (!projectId) {
+ 				return new Response(
+ 					JSON.stringify({
+ 						success: false,
+ 						error: {
+ 							code: "PROJECT_ID_REQUIRED",
+ 							message: "请求参数 projectId 不能为空"
+ 						},
+ 						status: 400
+ 					}),
+ 					{
+ 						status: 400,
+ 						headers: { "Content-Type": "application/json" }
+ 					}
+ 				)
+ 			}
+ 		},
+ 	],
  	finally: [corsify],
  })
```

---

已为你在 `handlers/index.ts` 的 router 初始化中增加了全局参数校验中间件：

- 会自动检查 `projectId` 是否存在于 query、header（`x-project-id`）、或 body（仅限 POST/PUT/DELETE 且为 JSON）。
- 若缺失，直接返回 400 错误，响应格式与现有风格一致。

你无需在各业务路由重复校验，所有 API 请求都会自动拦截校验 `projectId`。

如需调整白名单或特殊接口跳过校验，可进一步扩展此中间件。  
如有其他需求请随时告知！

---

_**User**_

我手动做了调整，是否可以在这里将解密后的projectId再传给下一个环节，让后续的流程能得到解密后的项目Id

---

_**Assistant**_

你的思路完全正确！  
在 itty-router 的中间件（before 钩子）中，可以通过在 `request` 对象上挂载自定义属性，将解密后的 `projectId` 传递给后续所有路由和中间件使用。

**推荐做法如下：**

1. 在中间件中将解密后的 `projectId` 挂载到 `request` 对象上（如 `request.projectId`）。
2. 后续所有 handler、业务逻辑都可以通过 `request.projectId` 直接获取到已解密的项目ID。

### 代码示例

```ts
export const router = AutoRouter({
  base: "/api/gateway",
  before: [
    preflight,
    async (request: Request & { projectId?: string }) => {
      const url = new URL(request.url)
      const projectIdStr = url.searchParams.get("projectId") as string
      const projectId = getProjectIdFromSHA256(projectIdStr)
      if (!projectId) {
        console.log("项目不存在或者解密失败", projectIdStr)
        return R.error("项目不存在")
      }
      // 挂载到 request 对象
      (request as any).projectId = projectId
    },
  ],
  finally: [corsify],
})
```

**注意事项：**
- TypeScript 类型上建议用 `(request as any).projectId = ...`，或者扩展类型声明。
- 后续所有 handler 都可以通过 `request.projectId` 获取到解密后的项目ID。

这样即可实现你想要的效果，且代码风格清晰、易维护！

如需进一步演示 handler 如何获取 `projectId`，可告知我。

---

