<!-- Generated by SpecStory -->

# 修改时间区间参数 (2025-04-26 11:44:10)

_**User**_

timeframe 的值改为类似2025-04-12 2025-04-26这样的时间区间，使用当前时间往前推14天

---

_**Assistant**_

我来帮你修改 `google-trend-task.listener.ts` 文件中的 `timeframe` 参数。我们需要将固定的 "now 14-d" 改为动态计算的时间区间。

首先，我会添加一个辅助函数来计算时间区间，然后修改 `execute` 方法中的相关代码。


---

```diff
  
  const logger = getLogger(import.meta.url)
+ 
+ /**
+  * 获取过去14天的时间区间
+  * @returns 格式为 "YYYY-MM-DD YYYY-MM-DD" 的时间区间字符串
+  */
+ function getLast14DaysTimeframe(): string {
+ 	const endDate = new Date()
+ 	const startDate = new Date()
+ 	startDate.setDate(endDate.getDate() - 14)
+ 	
+ 	const formatDate = (date: Date) => {
+ 		const year = date.getFullYear()
+ 		const month = String(date.getMonth() + 1).padStart(2, '0')
+ 		const day = String(date.getDate()).padStart(2, '0')
+ 		return `${year}-${month}-${day}`
+ 	}
+ 	
+ 	return `${formatDate(startDate)} ${formatDate(endDate)}`
+ }
  
  /**
```

```diff
  				} = await interest_over_time({
  					keywords: [word],
- 					timeframe: "now 14-d",
+ 					timeframe: getLast14DaysTimeframe(),
  					geo: "US",
  					hl: "zh-CN",
```

---

我已经完成了以下修改：

1. 添加了一个新的辅助函数 `getLast14DaysTimeframe()`，它会：
   - 获取当前日期作为结束日期
   - 计算14天前的日期作为开始日期
   - 将日期格式化为 "YYYY-MM-DD YYYY-MM-DD" 的格式

2. 在 `execute` 方法中，将原来的 `timeframe: "now 14-d"` 替换为 `timeframe: getLast14DaysTimeframe()`

现在，每次执行任务时，都会动态计算过去14天的时间区间。例如，如果今天是 2024-04-26，那么时间区间将会是 "2024-04-12 2024-04-26"。

这个修改保持了原有的14天时间范围，但使用了更精确的日期格式，符合你的要求。

---

