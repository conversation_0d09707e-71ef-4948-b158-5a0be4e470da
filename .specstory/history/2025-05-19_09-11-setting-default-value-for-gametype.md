<!-- Generated by SpecStory -->

# Setting Default Value for GameType (2025-05-19 17:11:01)

_**User**_

当前页面 我想对gameType设置一个默认值，但是没有生效

---

_**Assistant**_

你在 `BasicInfoTab` 组件中，确实尝试通过 `form.reset({ "gameType": GameType.Iframe })` 设置 `gameType` 的默认值，但这样做并不能保证“默认值”生效，原因如下：

### 你的代码逻辑分析

1. 你在 `useForm` 初始化时：
   ```ts
   const form = useForm<FormData>({
     resolver: zodResolver(formSchema),
     defaultValues: {} as FormData,
   })
   ```
   这里 `defaultValues` 传的是空对象，没有设置 `gameType` 的默认值。

2. 你在 `useEffect` 里根据 `data` 是否存在来 `reset` 表单：
   ```ts
   useEffect(() => {
     if (data) {
       form.reset(data as FormData)
     } else {
       form.reset({
         "gameType": GameType.Iframe,
       })
     }
   }, [data, form])
   ```
   这段代码的逻辑是：如果 `data` 没有加载出来，就重置为 `{ gameType: GameType.Iframe }`。但如果 `data` 是 `undefined` 或 `null`，`reset` 只会在 `data` 变化时触发一次。**如果 `data` 很快变成有值，`reset` 又会被调用，覆盖掉你设置的默认值。**

3. 你在 `useForm` 初始化时没有设置 `gameType`，所以第一次渲染时 `gameType` 其实是 `undefined`。

---

## 正确设置默认值的方式

### 推荐做法

**在 `useForm` 的 `defaultValues` 里直接设置默认值**，而不是在 `useEffect` 里 `reset`。

例如：

```ts
const form = useForm<FormData>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    gameType: GameType.Iframe,
    // 其他默认值...
  } as FormData,
})
```

然后在 `useEffect` 里只在 `data` 存在时 `reset`，不要在 `data` 不存在时再 `reset`，否则会导致“闪烁”或覆盖。

```ts
useEffect(() => {
  if (data) {
    form.reset(data as FormData)
  }
}, [data, form])
```

这样，初始渲染时 `gameType` 就是 `Iframe`，等数据加载出来后再用 `reset` 覆盖。

---

## 总结

- **默认值应该在 `useForm` 的 `defaultValues` 里设置。**
- `reset` 只在有数据时调用，避免无数据时反复 reset。
- 这样 `gameType` 的默认值就能生效，且不会被后续数据加载覆盖。

---

如需代码修改示例或有其他需求，请告知！

---

