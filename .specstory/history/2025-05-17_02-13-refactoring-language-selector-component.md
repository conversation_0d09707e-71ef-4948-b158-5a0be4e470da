<!-- Generated by SpecStory -->

# Refactoring Language Selector Component (2025-05-17 10:13:31)

_**User**_

'use client';

import { getCurrentLocaleName, localeNames } from '@/lib/i18n/locales';
import { Link } from '@/lib/i18n/navigation';
import { Button } from '@/lib/ui/components/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/lib/ui/components/dropdown-menu';
import { cn } from '@/lib/utils/commons';
import { ChevronDown, ChevronRight, Languages } from 'lucide-react';
import { useLocale } from 'next-intl';
import { usePathname } from 'next/navigation';

interface LocaleDropdownProps {
  type?: 'link' | 'button';
}

export function LocaleDropdown({ type = 'button' }: LocaleDropdownProps) {
  const pathname = usePathname();
  const locale = useLocale();
  // 从pathname中移除当前locale前缀,并处理特殊情况
  const cleanPathname = pathname === `/${locale}` ? '/' : pathname.replace(new RegExp(`^/${locale}`), '');
  const currentLocaleName = getCurrentLocaleName(locale);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {type === 'button' ? (
          <Button 
            variant="ghost" 
            className="text-navbar-foreground/80 hover:bg-navbar-foreground/10 hover:text-navbar-foreground transition-colors" 
            size="sm"
          >
            <Languages className="mr-2 h-4 w-4" />
            {currentLocaleName}
          </Button>
        ) : (
          <Button 
            variant="ghost" 
            className="w-full justify-start text-navbar-foreground/80 hover:bg-navbar-foreground/10 hover:text-navbar-foreground transition-colors"
          >
            <Languages className="mr-2 h-4 w-4" />
            {currentLocaleName}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="start" 
        className="w-[400px] bg-background/95 backdrop-blur-sm p-4 border-border"
      >
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(localeNames).map(([key, name]) => (
            <DropdownMenuItem key={key} asChild>
              <Link
                className={cn(
                  'flex items-center gap-2 w-full rounded-md px-2 py-2 hover:bg-navbar-foreground/10 cursor-pointer',
                  locale === key ? 'text-navbar-foreground bg-navbar-foreground/10' : 'text-navbar-foreground/80 hover:text-navbar-foreground'
                )}
                href={cleanPathname}
                locale={key==="en"?".":key}
              >
                <ChevronRight className="h-4 w-4" />
                {name}
              </Link>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 

参考上面的代码逻辑，重新美化一下这个语言选择页面，请直接使用LInk方式

---

_**Assistant**_

你希望参考 LocaleDropdown 的美化和交互逻辑，重构并美化当前的 LanguageSelector 组件，要求直接使用 Link 方式（即每个语言切换项都是一个 Link），并且风格更现代美观。下面是详细的重构思路：

### 设计思路

1. **使用 DropdownMenu 组件**：采用下拉菜单展示所有语言选项，点击按钮弹出语言列表。
2. **每个语言项为 Link**：每个语言切换项直接是一个 Link，点击后跳转到对应语言页面。
3. **当前语言高亮**：当前选中语言高亮显示。
4. **美化样式**：使用 TailwindCSS，风格与 LocaleDropdown 保持一致。
5. **类型安全**：保持 TypeScript 类型安全。

---

### 重构后的 LanguageSelector 组件

```tsx
"use client";

import React from "react";
import { useLocale } from "next-intl";
import { usePathname } from "next/navigation";
import { Link } from "@/lib/i18n/navigation";
import { ChevronDown, ChevronRight, Languages } from "lucide-react";
import { cn } from "@/lib/utils/commons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/lib/ui/components/dropdown-menu";
import { Language } from "@/lib/types";

interface LanguageSelectorProps {
  languages: Record<string, Language>;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  languages,
}) => {
  const locale = useLocale();
  const pathname = usePathname();

  // 去除当前 locale 前缀，特殊情况处理
  const cleanPathname =
    pathname === `/${locale}` ? "/" : pathname.replace(new RegExp(`^/${locale}`), "");

  const currentLang = languages[locale];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="flex items-center bg-primary/80 text-primary-foreground rounded px-3 py-1 text-sm hover:bg-primary/90 transition-colors"
        >
          <Languages className="mr-2 h-4 w-4" />
          {currentLang?.localName ?? locale}
          <ChevronDown className="ml-2 h-4 w-4" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="w-56 bg-background/95 backdrop-blur-sm p-2 border border-border"
      >
        <div className="flex flex-col gap-1">
          {Object.entries(languages).map(([key, lang]) => (
            <DropdownMenuItem key={key} asChild>
              <Link
                className={cn(
                  "flex items-center gap-2 w-full rounded-md px-2 py-2 hover:bg-primary/10 cursor-pointer transition-colors",
                  locale === key
                    ? "text-primary bg-primary/10 font-semibold"
                    : "text-primary-foreground/80 hover:text-primary"
                )}
                href={cleanPathname}
                locale={key}
              >
                <ChevronRight className="h-4 w-4" />
                {lang.localName}
              </Link>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSelector;
```

---

### 说明

- 直接用 Link 进行跳转，避免 select 的原生下拉。
- 当前语言高亮，其他语言 hover 有背景色。
- 保持按钮和下拉菜单风格统一，使用 TailwindCSS。
- 组件类型和 props 明确，符合 TypeScript 最佳实践。
- 你可以根据实际 UI 需求微调样式类名。

如需进一步自定义样式或交互，请告知你的具体需求！

---

_**User**_

icon图片能否使用对应语言代码所属的主要国家图标

---

_**Assistant**_

可以，完全可以为每个语言选项前加上对应国家的国旗图标，这样用户体验会更直观。常见做法有两种：

1. **使用 emoji 国旗**（简单、无依赖，兼容性好，适合大部分场景）
2. **使用专业的国旗图标库**（如 react-country-flag、flagpack、svg 图标等，适合更精致的 UI）

### 推荐方案：使用 emoji 国旗

你可以为每个语言配置一个 `flag` 字段，或者在渲染时通过语言代码映射到 emoji。例如：

```ts
const flagMap: Record<string, string> = {
  en: "🇺🇸",
  zh: "🇨🇳",
  ja: "🇯🇵",
  fr: "🇫🇷",
  de: "🇩🇪",
  // ...其他语言
};
```

然后在渲染时：

```tsx
<span className="mr-2 text-lg">{flagMap[key] ?? "🏳️"}</span>
```

### 进阶方案：使用 react-country-flag

如果你想用 SVG 国旗，可以安装 [react-country-flag](https://www.npmjs.com/package/react-country-flag)：

```bash
npm install react-country-flag
```

然后：

```tsx
import ReactCountryFlag from "react-country-flag";

// 语言代码到国家代码的映射
const countryMap: Record<string, string> = {
  en: "US",
  zh: "CN",
  ja: "JP",
  fr: "FR",
  de: "DE",
  // ...
};

<ReactCountryFlag
  countryCode={countryMap[key]}
  svg
  style={{
    width: "1.5em",
    height: "1.5em",
    marginRight: "0.5em",
    borderRadius: "50%",
  }}
/>
```

---

### 代码示例（emoji 方案）

只需在 Link 内部加一行即可：

```tsx
<span className="mr-2 text-lg">{flagMap[key] ?? "🏳️"}</span>
{lang.localName}
```

---

### 总结

- **emoji 方案**：简单、无依赖，适合大多数场景。
- **SVG 图标方案**：更美观，适合对 UI 有高要求的场景。

如需具体实现某种方案，请告知你更倾向哪种方式，我可以帮你直接补全代码！

---

_**User**_

整体下拉框的样式背景和文字颜色要与主题色保持一致，还需要兼容dark模式

---

_**Assistant**_

你提出的需求非常合理，下面是优化建议和具体实现方法：

---

## 1. 主题色与 dark 模式兼容的样式建议

- **背景色**：使用 Tailwind 的 `bg-primary`、`bg-primary/90`、`bg-background`、`bg-navbar` 等，并结合 dark: 前缀。
- **文字色**：用 `text-primary-foreground`、`text-navbar-foreground` 等，并结合 dark: 前缀。
- **hover 效果**：hover 时用主题色的浅色或透明度变化，dark 模式下也要有对应变化。
- **下拉菜单阴影**：可用 `shadow-lg`，并加 `backdrop-blur` 提升质感。
- **分割线/边框**：用 `border-border`，并加 dark 兼容。

---

## 2. 代码优化建议

- `DropdownMenuContent`、`DropdownMenuItem`、`Link` 等都要加上主题色和 dark 兼容的 class。
- 推荐将 flag emoji 加上 `mr-2` 和 `text-lg`，让国旗和文字对齐更美观。
- 你可以根据实际主题色变量调整 class 名。

---

## 3. 代码示例

请参考如下优化后的代码片段：

```tsx
<DropdownMenuContent
  align="start"
  className="
    w-56 
    bg-primary/95 
    text-primary-foreground
    dark:bg-primary/80 
    dark:text-primary-foreground
    backdrop-blur-md 
    p-2 
    border border-border 
    dark:border-border
    shadow-lg
  "
>
  <div className="flex flex-col gap-1">
    {Object.entries(languages).map(([key, lang]) => (
      <DropdownMenuItem key={key} asChild>
        <Link
          className={cn(
            "flex items-center gap-2 w-full rounded-md px-2 py-2 transition-colors cursor-pointer",
            locale === key
              ? "bg-navbar-foreground/10 text-navbar-foreground font-semibold dark:bg-navbar-foreground/20"
              : "hover:bg-navbar-foreground/10 text-navbar-foreground/80 hover:text-navbar-foreground dark:hover:bg-navbar-foreground/20"
          )}
          href={cleanPathname}
          locale={key}
        >
          <span className="mr-2 text-lg">{flagMap[key] ?? "🏳️"}</span>
          {lang.localName}
        </Link>
      </DropdownMenuItem>
    ))}
  </div>
</DropdownMenuContent>
```

### 其他建议

- 如果你的主题色变量不同，请将 `bg-primary`、`text-primary-foreground` 等替换为你项目实际的主题色 class。
- 如果你有全局的 dark 主题切换，Tailwind 的 `dark:` 前缀会自动生效。

---

## 4. 总结

- 主题色和 dark 模式兼容，主要靠 Tailwind 的 `dark:` 前缀和主题色变量。
- 保证下拉菜单、按钮、选项等都用主题色和前景色。
- hover、active、选中等状态都要兼容 dark 模式。

如需进一步微调（如具体颜色、阴影、圆角等），请告知你的主题色变量或设计规范，我可以帮你更精细调整！

---

_**User**_

增加一点圆角

---

_**Assistant**_

你已经在下拉菜单和按钮上使用了 `rounded` 和 `rounded-md`，但如果想让圆角更明显，可以将圆角类提升为 `rounded-lg` 或 `rounded-xl`，并确保所有相关元素都统一使用更大的圆角。

### 建议修改点

- 按钮：`rounded` → `rounded-lg`
- 下拉菜单容器：`rounded-md` → `rounded-lg`
- 每个选项：`rounded-md` → `rounded-lg`

---

### 关键代码片段调整

```tsx
<DropdownMenuTrigger asChild>
  <button
    className="flex items-center bg-primary/80 text-primary-foreground rounded-lg px-3 py-1 text-sm hover:bg-primary/90 transition-colors"
  >
    {/* ... */}
  </button>
</DropdownMenuTrigger>
<DropdownMenuContent
  align="start"
  className="
    w-56 
    bg-primary/95 
    text-primary-foreground
    dark:bg-primary/80 
    dark:text-primary-foreground
    backdrop-blur-md 
    p-2 
    rounded-lg
    border border-border 
    dark:border-border
    shadow-lg
  "
>
  <div className="flex flex-col gap-1">
    {Object.entries(languages).map(([key, lang]) => (
      <DropdownMenuItem key={key} asChild>
        <Link
          className={cn(
            "flex items-center gap-2 w-full rounded-lg px-2 py-2 transition-colors cursor-pointer",
            locale === key
              ? "bg-navbar-foreground/10 text-navbar-foreground font-semibold dark:bg-navbar-foreground/20"
              : "hover:bg-navbar-foreground/10 text-navbar-foreground/80 hover:text-navbar-foreground dark:hover:bg-navbar-foreground/20"
          )}
          href={cleanPathname}
          locale={key}
        >
          <span className="mr-2 text-lg">{flagMap[key] ?? "🏳️"}</span>
          {lang.localName}
        </Link>
      </DropdownMenuItem>
    ))}
  </div>
</DropdownMenuContent>
```

---

### 总结

- 将所有 `rounded-md` 和 `rounded` 替换为 `rounded-lg`，圆角会更加明显且风格统一。
- 你也可以根据实际需求使用 `rounded-xl` 获得更夸张的圆角效果。

如需进一步美化（如阴影、边框等），可随时告知！

---

