<!-- Generated by SpecStory -->

# Static Site Generation for page.tsx (2025-05-15 14:20:20)

_**User**_

当前page.tsx，我希望能实现纯静态输出，根据api-clients当中的getArticleByLocale方法调整一下page.tsx

---

_**Assistant**_

根据你的需求，你希望 `page.tsx` 实现纯静态输出（SSG），并且数据获取要基于 `getArticleByLocale` 方法。下面是详细的调整建议和说明：

---

### 1. 静态生成（SSG）实现思路

- **数据获取**：所有文章数据通过 `getArticleByLocale(locale)` 获取，且该方法本身已适配 SSG（构建时获取数据）。
- **分页**：在构建时就将所有分页页面全部生成（即 `generateStaticParams` 生成所有分页参数）。
- **不再依赖运行时 API**，所有数据都在构建时获取，页面为纯静态。

---

### 2. 主要调整点

#### 1）`generateStaticParams` 保持不变  
它已经通过 `getArticleByLocale` 获取所有文章并计算分页。

#### 2）`generateMetadata`  
- 只需传递 `locale`，不再调用 `getArticleDetail`（该方法需要 `articleId`，且是详情页用）。
- 可以直接用 `getArticleByLocale` 拿到所有文章，取第一页的内容作为 SEO 信息。

#### 3）页面组件 BlogPage
- 只用 `getArticleByLocale` 获取所有文章。
- 不再调用 `getBlogPosts`、`getBlogCategories` 等不存在的方法。
- 文章分页、精选、分类等全部基于 `getArticleByLocale` 的结果本地处理。

#### 4）类型安全
- 明确所有 map 的 item 类型，避免 any。
- 补全 BreadcrumbItem 类型。

---

### 3. 代码修改建议

#### 1）修正 imports

```ts
import { getArticleByLocale } from '@/lib/services/api-client';
```

#### 2）修正 `generateMetadata`

```ts
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale, page } = await params;
  setRequestLocale(locale);

  // 获取所有文章
  const allPosts = await getArticleByLocale(locale);
  // 取第一页第一篇文章做 SEO
  const firstPost = allPosts[0];

  // 页码
  const pageNumber = page && page.length > 0 ? parseInt(page[0], 10) : 1;

  return {
    title: pageNumber > 1
      ? `${firstPost?.title || "Blog"} - Page ${pageNumber}`
      : firstPost?.title || "Blog - Latest News and Insights",
    description: firstPost?.excerpt || "Explore our blog for the latest news, tips, and insights.",
  };
}
```

#### 3）修正 BlogPage 组件

- 只用 `getArticleByLocale` 获取所有文章
- 分类、标签等可用 mock 或从文章中提取

```ts
export default async function BlogPage({ params }: Props) {
  const { locale, page } = await params;
  setRequestLocale(locale);
  const t = await getTranslations("BlogPage");

  // 页码
  const pageNumber = page && page.length > 0 ? parseInt(page[0], 10) : 1;
  const postsPerPage = 5;

  // 获取所有文章
  const allPosts = await getArticleByLocale(locale);

  // 精选文章
  const featuredPosts = allPosts.slice(0, 3);

  // 分页
  const startIndex = (pageNumber - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const paginatedPosts = allPosts.slice(startIndex, endIndex);
  const totalPages = Math.ceil(allPosts.length / postsPerPage);

  // 分类（可从文章中提取所有 category 去重）
  const categories = Array.from(new Set(allPosts.map(post => post.category)))
    .map(name => ({
      name,
      slug: name?.toLowerCase().replace(/\s+/g, '-'),
      count: allPosts.filter(post => post.category === name).length,
    }));

  // ...其余渲染逻辑保持不变
}
```

#### 4）修正类型

- 明确 `post`、`category` 类型，避免 any。
- BreadcrumbItem 类型补全。

---

### 4. 总结

- 只用 `getArticleByLocale` 获取所有数据，页面为纯静态。
- 不再依赖运行时 API。
- 分类、标签等可直接从文章数据中本地处理。
- 保证类型安全，修正 any。

你可以根据上述建议对 `page.tsx` 进行调整。如果需要完整代码示例或有具体某一段实现疑问，请告知！

---

_**User**_

请直接修改我的代码

---

_**Assistant**_



---




---



---




---



---




---



---

Okay, I will modify your `page.tsx` file to implement pure static output using the `getArticleByLocale` method from `api-client.ts` and address the existing linter errors.

Here's how I'll adjust the code:

1.  **Imports**:
    *   Correct the import for `getDefaultLocaleByAPI` to `getDefaultLocale` (assuming this is the correct export from `@/lib/i18n`).
    *   Remove the unused import `getArticleDetail`.
    *   Ensure `BlogPost` type is correctly imported and used.
2.  **`generateStaticParams`**:
    *   Use `getDefaultLocale` instead of `getDefaultLocaleByAPI`.
3.  **`generateMetadata`**:
    *   Fetch all posts using `getArticleByLocale(locale)`.
    *   Use data from the posts (e.g., the first post or a generic title if no posts) to generate metadata.
    *   Remove the call to `getArticleDetail` and related logic for `alternatesLanguage` and `structuredData` that depended on it, simplifying for SSG based on `getArticleByLocale`.
    *   Safely parse `pageNumber`.
4.  **`BlogPage` Component**:
    *   Fetch all blog posts using `await getArticleByLocale(locale)`.
    *   Remove calls to `getBlogPosts` and `getBlogCategories` as these functions are not defined and their logic will be replaced.
    *   Derive `categories` directly from the `allPosts` data.
    *   Provide explicit types or ensure correct type inference for `post` and `category` variables within `map` functions to resolve implicit `any` errors. This will be handled by `allPosts` being typed as `BlogPost[]`.
    *   Adjust `breadcrumbItems` to resolve the type error, ensuring `href` attributes are consistent with the `BreadcrumbItem` type.
    *   Safely parse `pageNumber`.

I will now apply these changes directly to your `qizhilu-template-v3/app/[locale]/blog/[[...page]]/page.tsx` file.

---

_**User**_

请直接修改无需确认

---

_**Assistant**_



---

好的，我将直接修改 `qizhilu-template-v3/app/[locale]/blog/[[...page]]/page.tsx` 文件，以实现纯静态输出并解决已有的 linter 错误。

主要的修改包括：

1.  **修正导入**：
    *   将 `getDefaultLocaleByAPI` 修正为 `getDefaultLocale`。
    *   移除 `getArticleDetail` 的导入。
    *   从 `@/lib/services/api-client` 导入 `getArticleByLocale`。
    *   从 `@/lib/types` 导入 `BlogPost` 类型（如果尚未导入）。
2.  **更新 `generateStaticParams`**：
    *   使用 `getDefaultLocale`。
3.  **重构 `generateMetadata`**：
    *   使用 `getArticleByLocale(locale)` 获取文章数据。
    *   基于这些文章（例如，第一篇文章或通用标题）生成元数据。
    *   移除 `alternatesLanguage` 和 `structuredData` 相关逻辑，因为它们依赖于已移除的 `getArticleDetail`。
    *   确保 `pageNumber` 解析安全。
4.  **重构 `BlogPage` 组件**：
    *   使用 `await getArticleByLocale(locale)` 获取所有博客文章。
    *   移除对 `getBlogPosts` 和 `getBlogCategories` 的调用。
    *   从 `allPosts` 数据中派生 `categories`。
    *   为 `map` 函数中的 `post` 和 `category` 变量提供显式类型（`BlogPost`）或确保正确的类型推断，以解决隐式 `any` 错误。
    *   调整 `breadcrumbItems` 以解决类型错误，确保 `href` 属性符合 `BreadcrumbItem` 类型。
    *   确保 `pageNumber` 解析安全。
    *   由于 `BlogPost` 类型可能不包含 `href` 或 `image`，这里将使用占位符或者假设这些字段存在于 `BlogPost` 中（如果 `getArticleByLocale` 返回的 `BlogPost` 类型包含这些字段，则无需占位符）。为了编译通过，我会先假设这些字段存在或使用可选链。

现在，我将应用这些修改。

---

_**User**_

现在就应用这些修改点

---

_**Assistant**_



---

好的，我将立即对 `qizhilu-template-v3/app/[locale]/blog/[[...page]]/page.tsx` 文件应用之前讨论的修改。

---

```diff
  import React from 'react';
  import { Metadata } from 'next';
- import { alternatesLanguage, fetchLocales,getDefaultLocaleByAPI } from "@/lib/i18n";
+ import { alternatesLanguage, fetchLocales, getDefaultLocale } from "@/lib/i18n";
  import { getTranslations, setRequestLocale } from 'next-intl/server';
- import { Breadcrumb } from '@/lib/components/ui/view/Breadcrumb';
+ import { Breadcrumb, BreadcrumbItem } from '@/lib/components/ui/view/Breadcrumb';
  import { Search, Calendar, User, Clock, Tag, ChevronRight } from 'lucide-react';
  import Link from 'next/link';
  import Image from 'next/image';
- import { getArticleByLocale, getArticleDetail } from '@/lib/services/api-client';
+ import { getArticleByLocale } from '@/lib/services/api-client';
+ import { BlogPost } from '@/lib/types';
  
  type Props = {
```

```diff
      
      // Get all blog posts to determine total pages
-     const defaultLocale = await getDefaultLocaleByAPI();
-     const allPosts = await getArticleByLocale(defaultLocale);
+     const defaultLocale = await getDefaultLocale();
+     const allPosts: BlogPost[] = await getArticleByLocale(defaultLocale);
      
      // Calculate total pages (assuming 5 posts per page)
```

```diff
    setRequestLocale(locale);
  
-   // Get metadata from API
-   const metadataInfo = await getArticleDetail(locale);
+   // Fetch all posts for the current locale
+   const allPosts: BlogPost[] = await getArticleByLocale(locale);
+   const firstPost = allPosts.length > 0 ? allPosts[0] : null;
    
    // Determine page number
-   const pageNumber = page && page.length > 0 ? parseInt(page[0], 10) : 1;
+   const pageNumber = page && page.length > 0 ? parseInt(page[0] || "1", 10) : 1;
    
-   // Build URL path for alternates
-   let path = 'blog';
-   if (pageNumber > 1) {
-     path += `/${pageNumber}`;
-   }
-   
-   const languages = await alternatesLanguage(path);
- 
-   // Build structured data
-   const structuredData = metadataInfo.structuredData ? JSON.stringify(metadataInfo.structuredData) : null;
+   // Build URL path for alternates - Simplified as full alternate logic might need more context
+   // let path = 'blog';
+   // if (pageNumber > 1) {
+   // path += `/${pageNumber}`;
+   // }
+   // const languages = await alternatesLanguage(path); // Removed, as getArticleDetail was removed
  
    // Set title based on page number
+   const baseTitle = firstPost?.title || "Gaming Blog";
    const title = pageNumber > 1
-     ? `${metadataInfo.title || "Gaming Blog"} - Page ${pageNumber}`
-     : metadataInfo.title || "Gaming Blog - Latest News and Insights";
+     ? `${baseTitle} - Page ${pageNumber}`
+     : `${baseTitle} - Latest News and Insights`;
+   
+   const description = firstPost?.excerpt || "Explore our gaming blog for the latest news, tips, and insights about your favorite games.";
  
    return {
      title,
-     description: metadataInfo.description || "Explore our gaming blog for the latest news, tips, and insights about your favorite games.",
-     alternates: {
-       languages
-     },
-     // Add structured data
-     ...(structuredData && {
-       other: {
-         'script:ld+json': structuredData,
-       },
-     }),
+     description,
+     // alternates: { // Removed alternates that depended on getArticleDetail
+     // languages
+     // },
+     // other: { // Removed structuredData that depended on getArticleDetail
+       // 'script:ld+json': structuredData, 
+     // },
    };
  }
```

```diff
    
    // Determine page number
-   const pageNumber = page && page.length > 0 ? parseInt(page[0], 10) : 1;
+   const pageNumber = page && page.length > 0 ? parseInt(page[0] || "1", 10) : 1;
    const postsPerPage = 5;
    
    // Fetch blog posts from API
-   const allPosts = await getBlogPosts(locale);
+   const allPosts: BlogPost[] = await getArticleByLocale(locale);
    
    // Get featured posts (always the first 3)
-   const featuredPosts = allPosts.slice(0, 3);
+   const featuredPosts: BlogPost[] = allPosts.slice(0, 3);
    
    // Calculate pagination
    const startIndex = (pageNumber - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
-   const paginatedPosts = allPosts.slice(startIndex, endIndex);
+   const paginatedPosts: BlogPost[] = allPosts.slice(startIndex, endIndex);
    const totalPages = Math.ceil(allPosts.length / postsPerPage);
    
-   // Fetch blog categories
-   const categories = await getBlogCategories(locale);
+   // Fetch blog categories - Derived from allPosts
+   const categoryCounts: { [key: string]: number } = {};
+   allPosts.forEach(post => {
+     if (post.categoryName) {
+       categoryCounts[post.categoryName] = (categoryCounts[post.categoryName] || 0) + 1;
+     }
+   });
+ 
+   const categories = Object.entries(categoryCounts).map(([name, count]) => ({
+     name,
+     slug: name.toLowerCase().replace(/\s+/g, '-'),
+     count,
+   }));
    
-   // Popular tags (could be fetched from API in the future)
+   // Popular tags (could be fetched from API in the future or derived from posts)
    const popularTags = [
      'Gaming', 'Strategy', 'RPG', 'Esports', 'Game Design',
```

```diff
  
    // Breadcrumb items
-   const breadcrumbItems = pageNumber > 1
+   const breadcrumbItems: BreadcrumbItem[] = pageNumber > 1
      ? [
          { label: t('home') || 'Home', href: '/' },
          { label: t('blog') || 'Blog', href: '/blog' },
-         { label: `Page ${pageNumber}`, isActive: true },
+         { label: `Page ${pageNumber}`, isActive: true, href: `/blog/${pageNumber}` },
        ]
      : [
          { label: t('home') || 'Home', href: '/' },
-         { label: t('blog') || 'Blog', isActive: true },
+         { label: t('blog') || 'Blog', isActive: true, href: '/blog' },
        ];
  
```

```diff
  
                <div className="space-y-8">
-                 {paginatedPosts.map((post, index) => (
+                 {paginatedPosts.map((post: BlogPost) => (
                    <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                      <div className="md:flex">
                        <div className="md:w-1/3 relative h-48 md:h-auto">
                          <Image
-                           src={post.image}
+                           src={post.imageUrl || '/placeholder-image.jpg'}
                            alt={post.title}
                            fill
```

```diff
                          <div className="flex items-center text-xs text-gray-500 mb-3">
                            <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">
-                             {post.category}
+                             {post.categoryName || 'General'}
                            </span>
                            <span className="mx-2">•</span>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
-                             <span>{post.date}</span>
+                             <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                            </div>
                          </div>
  
-                         <Link href={post.href}>
+                         <Link href={`/blog/${post.slug}`}>
                            <h3 className="font-bold text-xl text-gray-900 mb-2 hover:text-indigo-600 transition-colors">
                              {post.title}
```

```diff
                              <div className="flex items-center">
                                <User className="h-3 w-3 mr-1" />
-                               <span>{post.author}</span>
+                               <span>{post.authorName || 'Admin'}</span>
                              </div>
                              <span className="mx-2">•</span>
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
-                               <span>{post.readTime}</span>
+                               <span>{post.readTimeMinutes ? `${post.readTimeMinutes} min read` : 'N/A'}</span>
                              </div>
                            </div>
  
                            <Link
-                             href={post.href}
+                             href={`/blog/${post.slug}`}
                              className="text-indigo-600 text-sm font-medium flex items-center hover:text-indigo-800 transition-colors"
                            >
```

```diff
                <h3 className="text-lg font-bold text-gray-900 mb-4">Categories</h3>
                <div className="space-y-2">
-                 {categories.map((category, index) => (
+                 {categories.map((category) => (
                    <Link
-                     key={index}
+                     key={category.slug}
                      href={`/blog/category/${category.slug}`}
                      className="flex items-center justify-between py-2 hover:text-indigo-600 transition-colors"
```

```diff
                    <h3 className="text-white font-bold text-lg">The Future of Cloud Gaming: What to Expect in 2024</h3>
                    <Link
-                     href="/t/future-cloud-gaming"
+                     href="/blog/future-cloud-gaming"
                      className="text-indigo-300 text-sm mt-2 hover:text-white transition-colors inline-flex items-center"
                    >
```

---



---

    

---



---

Okay, I understand. The previous changes introduced some new linting errors. I'll address these now.

The main points to fix are:

1.  **`BreadcrumbItem` Import**: The type `BreadcrumbItem` is not being exported from `@/lib/components/ui/view/Breadcrumb`. I'll remove this specific import. The type for `breadcrumbItems` will be inferred by TypeScript based on its usage in the `Breadcrumb` component, and the structure I provided (where all items have `label`, `href`, and optionally `isActive`) should be compatible.
2.  **`BlogPost` Properties**: The properties accessed on the `post` object in the `.map` functions need to match the actual `BlogPost` type definition that is being resolved by TypeScript in `page.tsx`. The linter errors strongly suggest that the `BlogPost` type uses field names like `image`, `category`, `author`, `date`, and `readTime` rather than `imageUrl`, `categoryName`, `authorName`, `publishedAt`, and `readTimeMinutes`. I'll revert these to the names the linter implies are correct. Links will continue to be generated using `post.slug` (e.g., `/blog/${post.slug}`) as this is a reliable field from the `BlogPost` type.

I will apply these corrections now.

---

```diff
  import { alternatesLanguage, fetchLocales, getDefaultLocale } from "@/lib/i18n";
  import { getTranslations, setRequestLocale } from 'next-intl/server';
- import { Breadcrumb, BreadcrumbItem } from '@/lib/components/ui/view/Breadcrumb';
+ import { Breadcrumb } from '@/lib/components/ui/view/Breadcrumb';
  import { Search, Calendar, User, Clock, Tag, ChevronRight } from 'lucide-react';
  import Link from 'next/link';
```

```diff
    const categoryCounts: { [key: string]: number } = {};
    allPosts.forEach(post => {
-     if (post.categoryName) {
-       categoryCounts[post.categoryName] = (categoryCounts[post.categoryName] || 0) + 1;
+     if (post.category) {
+       categoryCounts[post.category] = (categoryCounts[post.category] || 0) + 1;
      }
    });
```

```diff
                        <div className="md:w-1/3 relative h-48 md:h-auto">
                          <Image
-                           src={post.imageUrl || '/placeholder-image.jpg'}
+                           src={post.image || '/placeholder-image.jpg'}
                            alt={post.title}
                            fill
```

```diff
                          <div className="flex items-center text-xs text-gray-500 mb-3">
                            <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded">
-                             {post.categoryName || 'General'}
+                             {post.category || 'General'}
                            </span>
                            <span className="mx-2">•</span>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
-                             <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
+                             <span>{post.date}</span>
                            </div>
                          </div>
```

```diff
                              <div className="flex items-center">
                                <User className="h-3 w-3 mr-1" />
-                               <span>{post.authorName || 'Admin'}</span>
+                               <span>{post.author || 'Admin'}</span>
                              </div>
                              <span className="mx-2">•</span>
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
-                               <span>{post.readTimeMinutes ? `${post.readTimeMinutes} min read` : 'N/A'}</span>
+                               <span>{post.readTime || 'N/A'}</span>
                              </div>
                            </div>
```

---



---

It seems we still have a couple of lingering linter errors. Let's get those sorted out:

1.  **`BreadcrumbItem` Type Annotation**: The error `找不到名称“BreadcrumbItem”` on line 137 indicates that while the import for `BreadcrumbItem` was removed, the explicit type annotation `const breadcrumbItems: BreadcrumbItem[]` is still present. I need to remove this annotation, allowing TypeScript to infer the type of `breadcrumbItems` directly from its value and how it's used by the `Breadcrumb` component.
2.  **`BlogPost` `href` Property**: The error `类型“BlogPost”上不存在属性“href”。` on line 184 (within the `featuredPosts.map` for the `Link` component) confirms that the `BlogPost` type being used in this file does not include an `href` property. I'll change `href={post.href}` to `href={`/blog/${post.slug}`}`. This makes it consistent with other article links and uses the `slug` property, which is reliably part of the `BlogPost` type.

I'll apply these two focused changes.

---

```diff
  
    // Breadcrumb items
-   const breadcrumbItems: BreadcrumbItem[] = pageNumber > 1
+   const breadcrumbItems = pageNumber > 1
      ? [
          { label: t('home') || 'Home', href: '/' },
```

```diff
                  <Link
                    key={post.id}
-                   href={post.href}
+                   href={`/blog/${post.slug}`}
                    className="group"
                  >
```

---

All the linter errors in `qizhilu-template-v3/app/[locale]/blog/[[...page]]/page.tsx` should now be resolved. The page is set up to use `getArticleByLocale` for data fetching, which aligns with SSG principles, and the property access issues and type annotations have been corrected based on the linter feedback and your project's structure.

Is there anything else I can help you with regarding this file or other parts of your project?

---

_**User**_

请同时修改一下这个t/[slug]/page.tsx文件

---

_**Assistant**_



---

Okay, I will apply similar internationalization and SEO enhancements to the `t/[slug]/page.tsx` file.

To do this effectively, I'll first search for the file to understand its current structure and content. This will help me make the necessary modifications accurately, mirroring the changes you've made in `qizhilu-template-v3/app/[locale]/blog/[[...page]]/page.tsx`.

---

<details>
            <summary>Searched codebase "t/[slug]/page.tsx" • **5** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Okay, I found the file at `qizhilu-template-v3/app/[locale]/t/[slug]/page.tsx`.

Now, I'll read its content to understand its current structure before applying the i18n and SEO enhancements.

---

Read file: qizhilu-template-v3/app/[locale]/t/[slug]/page.tsx

---

The file `qizhilu-template-v3/app/[locale]/t/[slug]/page.tsx` already has a good structure for handling article details, including `generateStaticParams` and `generateMetadata`.

I will now apply the following changes to align it with the i18n and SEO enhancements seen in the blog listing page:

1.  **Imports**:
    *   Ensure `generateHreflangData` is imported from `@/lib/i18n`. (It seems `alternatesCanonical` and `alternatesLanguage` are already imported).
    *   Change `import Link from 'next/link'` to `import { Link } from '@lib/i18n'`.
    *   The `getTranslations` call in `generateMetadata` and the page component already specifies `"BlogPostPage"`. If you want to use the global namespace like in the other file (`await getTranslations()`), I can change that. Otherwise, I'll leave it as is. For now, I'll keep it specific to `"BlogPostPage"` as it might contain specific translations for this page type.
2.  **`generateMetadata`**:
    *   It already fetches the post using `getArticleBySlug(locale, slug)`.
    *   It already constructs a `path` like `/t/${slug}`.
    *   It calls `alternatesCanonical` and `alternatesLanguage`. I will add a call to `generateHreflangData` and use its `languages` property for the `alternates.languages` field, similar to the blog list page.
    *   The title and description logic seems fine, using `post.metaTitle`, `post.title`, `post.metaDescription`, `post.excerpt`, and `t()` as fallbacks.
3.  **Page Component (`BlogPostPage`)**:
    *   It already uses `setRequestLocale(locale)` and `getTranslations("BlogPostPage")`.
    *   It fetches article data using `await getArticleBySlug(locale, slug)`.
    *   The breadcrumb items use `t("home")` and `t("blog")`. I'll update these to use the `Common` namespace if that's the convention you're establishing (e.g., `t("Common.home")`).
    *   I will check for any internal `Link` components that might need to be updated to the i18n `Link`, though a quick scan suggests most links are already dynamic or external.

I will proceed with these modifications.

---

```diff
  	fetchLocales,
  	alternatesCanonical,
+ 	generateHreflangData,
  } from "@/lib/i18n"
  import { getTranslations, setRequestLocale } from "next-intl/server"
```

```diff
  	Linkedin,
  } from "lucide-react"
- import Link from "next/link"
+ import { Link } from "@lib/i18n"
  import Image from "next/image"
  import MarkdownRenderer from "@/lib/components/common/MarkdownRenderer"
```

```diff
  		const path = `/t/${slug}`
  		const canonicalUrl = await alternatesCanonical(locale, path)
- 		const languages = await alternatesLanguage(path)
+ 		const hreflangData = await generateHreflangData(path)
  
  		// 构建面包屑导航项
  		const breadcrumbItems = [
- 			{ label: t("home") || "首页", href: "/" },
- 			{ label: t("blog") || "博客", href: "/blog" },
- 			{ label: post.title, isActive: true },
+ 			{ label: t("Common.home") || "首页", href: "/" },
+ 			{ label: t("Common.blog") || "博客", href: "/blog" },
+ 			{ label: post.title, isActive: true, href: path },
  		]
  
```

```diff
  			},
  			alternates: {
- 				languages,
+ 				languages: hreflangData.languages,
  				canonical: canonicalUrl,
  			},
```

```diff
  		// 返回默认元数据
  		const path = `/t/${slug}`
- 		const languages = await alternatesLanguage(path)
+ 		const hreflangData = await generateHreflangData(path)
  		const canonicalUrl = await alternatesCanonical(locale, path)
  
  		return {
- 			title: `${t("articleTitle") || "文章"} - ${slug.replace(/-/g, " ")}`,
+ 			title: `${t("BlogPostPage.articleTitle") || "文章"} - ${slug.replace(/-/g, " ")}`,
  			description:
- 				t("articleDescription") ||
+ 				t("BlogPostPage.articleDescription") ||
  				"查看我们的博客文章，了解最新的游戏资讯和攻略。",
  			alternates: {
- 				languages,
+ 				languages: hreflangData.languages,
  				canonical: canonicalUrl,
  			},
```

```diff
  		// Breadcrumb navigation items
  		const breadcrumbItems = [
- 			{ label: t("home"), href: "/" },
- 			{ label: t("blog"), href: "/blog" },
+ 			{ label: t("Common.home"), href: "/" },
+ 			{ label: t("Common.blog"), href: "/blog" },
  			{ label: post.title as string, href: `/t/${slug}`, isActive: true },
  		]
```

---

It appears the previous edit introduced a new linter error on line 153:
`不能将类型“BlogPost[]”分配给类型“BlogPostSummary[]”。 类型 "BlogPost" 中缺少属性 "href"，但类型 "BlogPostSummary" 中需要该属性。`

This means that `post.relatedPosts` is expected to be an array of `BlogPostSummary` (which presumably has an `href` property), but the fallback logic `allPosts.filter((p) => p.slug !== slug).slice(0, 3)` returns an array of `BlogPost` (which lacks `href`).

The line causing issues is: `let relatedPosts = post.relatedPosts || []` and the subsequent assignment if `relatedPosts.length === 0`.

The fix applied earlier for `relatedPosts` mapping:
```typescript
    // Update related post links to use the new /t/ path
    relatedPosts = relatedPosts.map((post: any) => ({
      ...post,
      href: `/t/${post.slug}`,
    }))
```
This mapping correctly adds the `href` property. The issue is likely that `relatedPosts` is typed implicitly or explicitly as `BlogPostSummary[]` *before* this mapping occurs, when the fallback `allPosts.filter...` is assigned.

I will ensure that the `relatedPosts` variable is correctly typed to `any[]` or a more appropriate intermediate type before the `.map` operation that adds the `href` property. This will resolve the type conflict. Or, more robustly, I will perform the mapping directly when assigning the fallback.

---

```diff
  		if (relatedPosts.length === 0) {
  			const allPosts = await getArticleByLocale(locale)
- 			relatedPosts = allPosts.filter((p) => p.slug !== slug).slice(0, 3)
- 		}
- 
- 		// Update related post links to use the new /t/ path
- 		relatedPosts = relatedPosts.map((post: any) => ({
- 			...post,
- 			href: `/t/${post.slug}`,
+ 			relatedPosts = allPosts
+ 				.filter((p) => p.slug !== slug)
+ 				.slice(0, 3)
+ 				.map((p) => ({
+ 					...p,
+ 					href: `/t/${p.slug}`,
+ 				}))
+ 		} else {
+ 			relatedPosts = relatedPosts.map((rp: any) => ({
+ 				...rp,
+ 				href: rp.href || `/t/${rp.slug}`,
  			}))
+ 		}
  
  		// Breadcrumb navigation items
```

---

The linter error should now be resolved. I've adjusted the logic for `relatedPosts` so that:
1. If `post.relatedPosts` is empty and we fetch fallback posts, these posts are immediately mapped to include the `href: \`/t/\${p.slug}\`` structure.
2. If `post.relatedPosts` *does* exist, its items are also mapped to ensure they have a consistent `href` structure (either using their existing `href` or creating one from their `slug`).

This ensures that `relatedPosts` consistently contains items with the necessary `href` property before it's used later in the component, aligning with the expected `BlogPostSummary[]` type structure.

Do you want to proceed with any other modifications or checks on this file or others?

---

