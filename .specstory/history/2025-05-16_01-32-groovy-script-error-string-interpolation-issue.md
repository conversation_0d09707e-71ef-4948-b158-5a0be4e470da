<!-- Generated by SpecStory -->

# Groovy Script Error: String Interpolation Issue (2025-05-16 09:32:53)

_**User**_

WorkflowScript: 218: illegal string body character after dollar sign;
   solution: either escape a literal dollar sign "\$5" or bracket the value expression "${5}" @ line 218, column 17.
                   echo "PID: $(cat ${PID_FILE})"
                   ^

1 error

	at org.codehaus.groovy.control.ErrorCollector.failIfErrors(ErrorCollector.java:309)

---

_**Assistant**_



---

您好！我注意到您的 `Jenkinsfile.dev` 文件中存在一个 Groovy 语法错误。

错误信息 `WorkflowScript: 218: illegal string body character after dollar sign; solution: either escape a literal dollar sign "\$5" or bracket the value expression "${5}" @ line 218, column 17.` 表明在第 218 行的 `echo "PID: $(cat ${PID_FILE})"` 中，`$(cat ${PID_FILE})` 这种语法在 Groovy 字符串中是非法的。Groovy 使用 `${expression}` 的形式进行表达式求值和字符串插值，而 `$(...)` 是 shell 命令替换的语法。

为了修复这个问题，我们需要使用 Jenkins Pipeline 提供的方法来读取文件内容。我将使用 `readFile` 步骤来读取 `PID_FILE` 文件的内容，并去除可能存在的多余空格。

具体的修改如下：
我将把 `qizhilu-template-v3/Jenkinsfile.dev` 文件中的：
`echo "PID: $(cat ${PID_FILE})"`
替换为：
`echo "PID: ${readFile(file: "${PID_FILE}").trim()}"`

这将确保在 Groovy 上下文中正确读取并显示 PID。

我现在将应用这个修改。

---

```diff
                  echo "Public URL: ${PUBLIC_URL}"
                  echo "Port: ${DEV_PORT}"
-                 echo "PID: $(cat ${PID_FILE})"
+                 echo "PID: ${readFile(file: "${PID_FILE}").trim()}"
                  echo "Log file: ${LOG_FILE}"
                  echo "--------------------------------------"
```

---

修改已应用。`Jenkinsfile.dev` 中第 218 行的语法错误已经修复，现在它会使用 `readFile` 来正确获取和显示 PID。

如果您有其他问题或需要进一步的帮助，请告诉我。

---

