<!-- Generated by SpecStory -->

# 字体用途探讨 (2025-05-05 14:56:03)

_**User**_

这里的三个字体是用于什么地方？

---

_**Assistant**_



---

这个CSS文件中定义了三种字体变量，它们分别用于不同的UI元素：

1. `--font-sans`（Plus Jakarta Sans）：无衬线字体，通常用于界面元素、导航栏、按钮、标题和正文内容，提供现代清晰的外观。

2. `--font-serif`（Source Serif 4）：衬线字体，常用于长篇文章、正式内容或需要更高阅读舒适度的文本部分。

3. `--font-mono`（JetBrains Mono）：等宽字体，主要用于显示代码块、命令行或需要字符对齐的内容。

这些字体变量在明暗两种主题模式下保持一致，通过CSS变量方式定义，可在整个应用中统一引用。

---

